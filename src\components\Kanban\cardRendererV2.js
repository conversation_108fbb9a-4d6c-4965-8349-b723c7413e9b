import { LuTags } from 'react-icons/lu';
import { Tooltip } from 'react-tooltip';
import { openHistorySidebar } from './kanbanFunctions';

export const renderCard = ({
  data,
  // firstStep,
  updatedAt,
  // column,
  taskId,
  customTaskId,
  tile = false,
  // page = 'End Flow',
  setHistorySidebar,
  card,
  tooltipIds,
  setShowSelectTags,
}) => {
  // function capitalizeFirstLetter(str) {
  //   if (!str) return;
  //   return str.charAt(0).toUpperCase() + str.slice(1);
  // }

  // function truncateId(id, len) {
  //   return id && id.length > len ? `${id.slice(0, len)}...` : id;
  // }
  return (
    <>
      <div className={`px-3 text-sm ${tile === true ? 'w-fit' : ''}`}>
        <div className={`flex items-start justify-between mb-3`}>
          <span
            className={`shrink-0 inline-flex items-center px-2.5 py-1.5 text-xs font-semibold bg-blue-600 text-white rounded-lg shadow-sm hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap ${tile === true ? 'hidden' : ''}`}
            {...(customTaskId && customTaskId?.length > 15
              ? { title: customTaskId || '' }
              : {})}
            onClick={() => {
              openHistorySidebar({ setHistorySidebar, card });
            }}
            data-tooltip-id={tooltipIds?.taskIdTooltip}
          >
            {/* Add a condition that if id is greater than 15 than it Breaks Ui so for handle just use an hover (title attribute) */}
            <Tooltip
              id={tooltipIds?.taskIdTooltip}
              style={{
                backgroundColor: '#1e293b',
                color: 'white',
                fontWeight: 'normal',
                padding: '12px',
                fontSize: '12px',
                maxWidth: '200px',
                zIndex: '1',
                borderRadius: '6px',
                lineHeight: '20px',
              }}
              arrowColor="transparent"
            >
              <p>
                {customTaskId
                  ? customTaskId.length > 20
                    ? `${customTaskId.slice(0, 20)}${taskId}...`
                    : `${customTaskId}${taskId}`
                  : taskId}
              </p>
            </Tooltip>
            {customTaskId
              ? customTaskId.length > 26
                ? `${customTaskId.slice(0, 26)}${taskId}...`
                : `${customTaskId}${taskId}`
              : taskId}
          </span>

          <div className="p-1.5 rounded-lg bg-purple-50 shadow-sm hover:bg-purple-100 transition-colors duration-200 cursor-pointer">
            <LuTags
              size={16}
              title="Edit Tags"
              onClick={() => setShowSelectTags(true)}
              className="text-purple-500"
            />
          </div>
          {/* <span
            className={`bg-orange-100 text-orange-500 px-3 py-1 rounded-lg font-medium whitespace-nowrap ${tile === true ? 'hidden' : ''}`}
            data-tooltip-id={tooltipIds?.stepIdTooltip}
          >
            <Tooltip
              id={tooltipIds?.stepIdTooltip}
              style={{
                backgroundColor: '#1e293b',
                color: 'white',
                fontWeight: 'normal',
                padding: '12px',
                fontSize: '12px',
                maxWidth: '200px',
                zIndex: '1',
                borderRadius: '6px',
                lineHeight: '20px',
              }}
              arrowColor="transparent"
            >
              <p>
                {firstStep?.data?.salesOrderID ||
                  firstStep?.data?.salesInquiryId ||
                  firstStep?.data?.workOrderId ||
                  firstStep?.data?.quoteID}
              </p>
            </Tooltip>
            {truncateId(firstStep?.data?.salesOrderID, 10) ||
              truncateId(firstStep?.data?.salesInquiryId, 10) ||
              truncateId(firstStep?.data?.workOrderId, 10) ||
              truncateId(firstStep?.data?.quoteID, 10)}
          </span> */}
          {/* <span
            className={`px-3 py-1 gap-5 items-center flex rounded-xl font-medium whitespace-nowrap ${tile === true ? '' : 'hidden'}`}
          >
            {page === 'End Flow' ? page : 'Create a new ' + column?.page?.label}
          </span> */}
          {/* {column?.page?.label !== 'Completed' && (
            <span
              className={`${
                data?.stepPage === column?.page?.label
                  ? 'bg-green-100 text-green-500'
                  : 'bg-yellow-100 text-yellow-500'
              }  px-3 py-1 rounded-lg font-medium whitespace-nowrap ${tile === true ? 'hidden' : ''}`}
            >
              {data?.stepPage === column?.page?.label ? 'Created' : 'Pending'}
            </span>
          )} */}
          {/* {data?.stepPage === 'Quotation' && (
            <div className="flex flex-wrap">
              <span
                className={`${
                  data?.data?.quoteStatus === 'Approved'
                    ? 'bg-green-100 text-green-500'
                    : data?.data?.quoteStatus === 'pending'
                      ? 'bg-yellow-100 text-yellow-500'
                      : 'bg-red-100 text-red-500'
                }  px-3 py-1 rounded-lg font-medium whitespace-nowrap ${tile === true ? 'hidden' : ''}`}
              >
                {`Quote ${capitalizeFirstLetter(data?.data?.quoteStatus)}`}
              </span>
            </div>
          )} */}
        </div>
        <div className="flex  gap-2 mt-2">
          <div className="flex items-center gap-2 truncate">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-2 h-2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
              />
            </svg>

            <p className="text-gray-400 !text-[9px] font-medium">
              {data?.userId?.name || 'name'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-2 h-2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
              />
            </svg>

            <p className="text-gray-400 !text-[9px] font-medium">
              {new Date(updatedAt).toDateString()}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};
