// import { useDrag } from 'react-dnd';
import { useContext, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { Store } from '../../store/Store';
// import { unCamelCaseString } from '../../helperFunction';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Tooltip } from 'react-tooltip';
import {
  addDurationToDate,
  // daysBetween,
  generatePrefixId,
  mobileWidth,
  tabletWidth,
} from '../../helperFunction';
import { apiSlice } from '../../slices/apiSlice';
import {
  useAddCustomTagsMutation,
  useAddMediaMutation,
  useArchiveOrderMutation,
  useDeleteMediaMutation,
  usePartiallyDoneMutation,
  useShiftKanbanColumnMutation,
  useUpdateAssignUsersMutation,
  useUpdateReminderDateAndTimeMutation,
} from '../../slices/orderApiSlice';
import { useGetAllEmployeesQuery } from '../../slices/userApiSlice';
import Button from '../global/components/Button';
import Modal from '../global/components/Modal';
import MultiSelect from '../global/components/MultiSelect';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import Textarea from '../global/components/Textarea';
import { Input } from '../v2';
import { renderCard } from './cardRenderer';
import { useMediaQuery } from 'react-responsive';

function Card({
  column,
  card,
  tile = false,
  setHistorySidebar,
  setInitialScrollIndex,
  index,
  deletedTags,
  tooltipIds,
}) {
  let blueTagClass =
    'bg-blue-100 cursor-pointer hover:bg-blue-500 hover:text-white transition text-blue-500 text-sm px-3 py-1 rounded-lg font-medium whitespace-nowrap';
  const navigate = useNavigate();
  const [shiftKanbanColumn, { isError: isCardShiftError }] =
    useShiftKanbanColumnMutation();
  const [partiallyDone, { isError: isPartialShiftError }] =
    usePartiallyDoneMutation();
  const [updateAssignUsers] = useUpdateAssignUsersMutation();
  const [archiveOrder] = useArchiveOrderMutation();
  const {
    defaults: { defaultParam },
    dispatch,
  } = useContext(Store);
  const [addMedia] = useAddMediaMutation();
  const [deleteMedia] = useDeleteMediaMutation();
  const dispatchFn = useDispatch();
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });

  const [tags, setTags] = useState([]);
  const [addCustomTags] = useAddCustomTagsMutation();
  const { data: employees = [] } = useGetAllEmployeesQuery();
  const { state: { user } = {} } = useContext(Store);
  const [updateReminderDateAndTime, { isLoading }] =
    useUpdateReminderDateAndTimeMutation();
  const [userModal, setUserModal] = useState(false);
  const [reminderModal, setReminderModal] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isReasonModalOpen, setIsReasonModalOpen] = useState(false);
  const [reasonForDelay, setReasonForDelay] = useState([
    { reason: '', delayDate: new Date().toISOString().split('T')[0] },
  ]);
  const [isCommonToAllChecked, setIsCommonToAllChecked] = useState(false);
  const [openShiftModal, setOpenShiftModal] = useState({
    open: false,
    isPartial: false,
  });
  const [selectAll, setSelectAll] = useState(false);
  const [showSelectTags, setShowSelectTags] = useState(false);
  const [selectedTags, setSelectedTags] = useState([]);
  const [remainingTime, setRemainingTime] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
    days: 0,
    isExceed: false,
  });

  const [reminderData, setReminderData] = useState({
    date: new Date().toISOString()?.split('T')[0],
    time: '',
  });
  const [reminderRemarks, setReminderRemarks] = useState('');

  useEffect(() => {
    const nodes = defaultParam?.departmentFlow?.nodeStructure?.nodes;
    if (nodes) {
      nodes.forEach((node) => {
        const { data } = node;
        if (data && data?.selectedPage) {
          if (data?.selectedPage[0]?.label === column?.page?.label) {
            setTags(data?.tags);
          }
        }
      });
    }
  }, [defaultParam, column?.page?.label]);

  useEffect(() => {
    const duration = column?.duration;
    // getting shift start hour
    const shiftStart = +defaultParam?.shiftStart?.split(':')[0];
    const shiftStartMinute = +defaultParam?.shiftStart?.split(':')[1];
    // getting shift end hour
    const shiftEnd = +defaultParam?.shiftStop?.split(':')[0];
    const shiftEndMinute = +defaultParam?.shiftStop?.split(':')[1];
    const creationTime =
      card?.createdAt === card?.updatedAt ? card?.createdAt : card?.updatedAt;

    if (duration) {
      const endTime = addDurationToDate(creationTime, duration);

      const intervalId = setInterval(() => {
        const currentTime = new Date();

        const withinShiftHours =
          currentTime.getHours() >= shiftStart &&
          (currentTime.getHours() < shiftEnd ||
            (currentTime.getHours() === shiftEnd &&
              currentTime.getMinutes() < shiftEndMinute));
        const withinShiftMinutes =
          currentTime.getMinutes() >= shiftStartMinute ||
          currentTime.getHours() > shiftStart;

        if (withinShiftHours && withinShiftMinutes) {
          const difference = endTime - currentTime;
          const updatedDate = new Date(Math.abs(difference));

          const days = Math.floor(Math.abs(difference) / (1000 * 60 * 60 * 24));
          const hours = updatedDate.getUTCHours();
          const minutes = updatedDate.getUTCMinutes();
          const seconds = updatedDate.getUTCSeconds();

          if (difference <= 0) {
            setRemainingTime({
              hours,
              minutes,
              seconds,
              days,
              isExceed: true,
            });
          } else {
            setRemainingTime({
              hours,
              minutes,
              seconds,
              days,
              isExceed: false,
            });
          }
        }
      }, 1000);

      return () => clearInterval(intervalId);
    }
  }, [column?.duration, card?.createdAt, card?.updatedAt, defaultParam]);

  const [flowArray, setFlowArray] = useState([]);

  useEffect(() => {
    setFlowArray(
      defaultParam?.departmentFlow?.nodeStructure?.nodes?.map((item) => ({
        ...item?.data?.selectedPage?.[0],
        title: column?.title,
      }))
    );
  }, [card?.currentStep, column, defaultParam]);

  useEffect(() => {
    if (isPartialShiftError) {
      dispatch({
        type: 'RESET_KANBAN_COLUMN_DATA',
      });
    }
  }, [isPartialShiftError, dispatch]);

  useEffect(() => {
    if (isCardShiftError) {
      dispatch({
        type: 'RESET_KANBAN_COLUMN_DATA',
      });
    }
  }, [isCardShiftError, dispatch]);
  const { _id } = card;
  const handleAssignTags = async () => {
    await addCustomTags({ _id, tags: selectedTags });
    setInitialScrollIndex(index);
    setShowSelectTags(false);
    toast.success('Tags Assigned SuccessFully');
  };

  useEffect(() => {
    const handleUpdateTags = async () => {
      const prevTags = card?.tags;
      const updatedTags = prevTags.filter((tag) => !deletedTags.includes(tag));
      const sentFormat = updatedTags?.map((tag) => ({
        value: tag,
        label: tag,
      }));
      await addCustomTags({ _id, tags: sentFormat });
      setInitialScrollIndex(index);
    };
    (async () => {
      if (deletedTags?.length > 0) {
        await handleUpdateTags();
      }
    })();
  }, [deletedTags]); //eslint-disable-line

  useEffect(() => {
    if (card?.tags?.length > 0) {
      const tagsArray = card?.tags?.map((tag) => ({ value: tag, label: tag }));
      setSelectedTags(tagsArray);
    }
  }, [card, deletedTags]);

  const handleShiftColumn = async ({ nextColumns, isPartial }) => {
    let columns = selectedColumns?.map((elem) => elem?.label);
    if (isPartial) {
      if (isLastColumn() === true) {
        await partiallyDone({
          id: card?._id,
          page: column?.page?.label,
          isComplete: true,
        });
        setOpenShiftModal({
          open: false,
          isPartial: false,
        });
        dispatch({
          type: 'PARTIALLY_SHIFT_CARD',
          payload: {
            data: card,
            nextColumns: ['Completed'],
            currentColumn: column?.page?.label,
          },
        });
        // await refetch();
      } else {
        await partiallyDone({
          id: card?._id,
          page: column?.page?.label,
          data: {
            nextColumns: nextColumns ? nextColumns : columns,
          },
          isComplete: false,
        });
        setOpenShiftModal({
          open: false,
          isPartial: false,
        });
        dispatch({
          type: 'PARTIALLY_SHIFT_CARD',
          payload: {
            data: card,
            nextColumns: nextColumns ? nextColumns : columns,
            currentColumn: column?.page?.label,
          },
        });
        // await refetch();
      }
    } else {
      if (isLastColumn() === true) {
        await shiftKanbanColumn({
          id: card?._id,
          page: column?.page?.label,
          isComplete: true,
        });
        setOpenShiftModal({
          open: false,
          isPartial: false,
        });
        dispatch({
          type: 'SHIFT_CARD',
          payload: {
            data: card,
            nextColumns: ['Completed'],
            currentColumn: column?.page?.label,
          },
        });
        // await refetch();
      } else {
        await shiftKanbanColumn({
          id: card?._id,
          page: column?.page?.label,
          data: {
            nextColumns: nextColumns ? nextColumns : columns,
          },
          isComplete: false,
        });
        dispatch({
          type: 'SHIFT_CARD',
          payload: {
            data: card,
            nextColumns: nextColumns ? nextColumns : columns,
            currentColumn: column?.page?.label,
          },
        });
        setOpenShiftModal({
          open: false,
          isPartial: false,
        });
        // await refetch();
      }
    }
  };

  const getStep = () => {
    for (let step of card?.steps) {
      if (column?.page?.label === step?.stepPage) {
        return step;
      }
    }
    return undefined;
  };

  const openHistorySidebar = () => {
    setHistorySidebar({
      open: true,
      steps: card?.steps,
      delayReasons: card?.delayReasons,
      orderId: card?._id,
    });
  };

  const isLastColumn = () => {
    let currentNode;
    // let step = getStep();
    for (let node of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
      if (node?.data?.selectedPage?.[0]?.label === column?.page?.label) {
        currentNode = node;
        break;
      }
    }

    let correspondingEdge;
    for (let edge of defaultParam?.departmentFlow?.nodeStructure?.edges) {
      if (edge?.target === currentNode?.id) {
        correspondingEdge = edge;
        break;
      }
    }

    if (correspondingEdge) {
      return false;
    } else {
      return true;
    }
  };
  const getNextColumns = () => {
    let currentNode;
    // let step = getStep();
    for (let node of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
      if (node?.data?.selectedPage?.[0]?.label === column?.page?.label) {
        currentNode = node;
        break;
      }
    }

    let correspondingNodes = [];
    for (let edge of defaultParam?.departmentFlow?.nodeStructure?.edges) {
      if (edge?.target === currentNode?.id) {
        correspondingNodes.push(edge?.source);
      }
    }
    let columns = [];
    for (let i in correspondingNodes) {
      for (let node of defaultParam?.departmentFlow?.nodeStructure?.nodes) {
        if (correspondingNodes[i] === node?.id) {
          columns.push(node?.data?.selectedPage?.[0]);
        }
      }
    }
    return columns;
  };
  const [selectedColumns, setSelectedColumns] = useState([]);

  useEffect(() => {
    if (defaultParam) {
      setSelectedColumns(getNextColumns().map((item) => item.label));
    }
  }, [defaultParam]); //eslint-disable-line

  const onUpgrade = async () => {
    let getSalesData = false;
    let prevStep = card?.currentStep - 1;
    if (
      column?.page?.label === 'Sales Inquiry' ||
      column?.page?.label === 'Quotation' ||
      column?.page?.label === 'Sales Order'
    ) {
      getSalesData = true;
      let prevSteps = [];
      for (let step of card?.steps) {
        if (step?.stepNumber === prevStep) {
          prevSteps.push(step);
        }
      }
      dispatch({
        type: 'SET_PREV_STEP',
        payload: {
          data: prevSteps,
        },
      });
    }
    let taskId =
      card?.taskId || generatePrefixId(defaultParam?.prefixIds?.['taskId']);
    navigate(
      `${column?.page?.value}?kanban=true&department=${column?.title}&page=${column?.page?.label}&orderId=${card?._id}&refType=${column?.page?.label}&getSalesData=${getSalesData}&taskID=${taskId}`
    );
  };

  const canUpgrade = () => {
    if (card?.currentStep === 0) return false;
    let step = getStep();
    if (step) {
      return false;
    } else {
      return true;
    }
  };

  const handleSelectAll = (e) => {
    if (e.target.checked) {
      const allValues = getNextColumns()?.map((item) => item.label);
      setSelectedColumns(allValues);
    } else {
      setSelectedColumns([]);
    }
    setSelectAll(e.target.checked);
  };

  const handleCheckBoxChange = (event, item) => {
    setSelectedColumns((prev) => {
      if (event.target.checked) {
        return [...prev, item];
      } else {
        return prev.filter((data) => data !== item);
      }
    });
    setSelectAll(false);
  };

  const handleUpdateRemainderDateAndTime = async () => {
    const res = await updateReminderDateAndTime({
      date: reminderData.date,
      time: reminderData.time,
      id: card?._id,
      reminderRemarks: reminderRemarks,
      reminderSetBy: user?._id,
    });

    if (res) {
      toast.success('Updated remainder time and date');
      setReminderModal(false);
    }
    setReminderRemarks('');
  };

  const handleUpdateAssignUser = async () => {
    let adminId;
    if (user.role === 'admin' || user.role === 'superuser') {
      adminId = user?._id;
    }
    const res = await updateAssignUsers({
      selectedUsers: selectedUsers,
      id: card?._id,
      adminId: adminId,
    });
    if (res) {
      toast.success('Assigned User Successfully');
      setUserModal(false);
    }
  };

  const handleArchive = async (id) => {
    await archiveOrder({
      id,
      data: { user: user._id, archive: card?.archive.includes(user?._id) },
    });
    dispatchFn(apiSlice.util.invalidateTags(['DepartmentalOrders']));
  };

  const setDatesForDelayReason = () => {
    const delayedTimeInDays = remainingTime?.days;
    if (delayedTimeInDays === 0) {
      return;
    }
    const currentTime = new Date();
    const startDate = new Date(currentTime);
    startDate.setDate(startDate.getDate() - delayedTimeInDays);
    // Generate delay reasons array
    const delayReasons = [];
    for (let i = 0; i < delayedTimeInDays; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const formattedDate = date.toISOString().split('T')[0];
      delayReasons.push({
        reason: ``,
        delayDate: formattedDate,
      });
    }
    if (delayReasons.length > 0) {
      setReasonForDelay(delayReasons);
    }
  };
  const handleFileChange = async (file) => {
    try {
      if (file) {
        await addMedia({ id: card?._id, file });
        toast.success('Media Added Successfully');
      }
    } catch (error) {
      toast.error(error);
    }
  };
  const handleDeleteMedia = async (id) => {
    try {
      await deleteMedia({ id: card?._id, mediaId: id });
      toast.success('Media Deleted Successfully');
    } catch (error) {
      toast.error(error);
    }
  };
  return (
    <>
      {openShiftModal?.open && (
        <Modal
          isMobile={isMobile}
          isTablet={isTablet}
          title="Select Next Columns"
          description="Select one or more columns to shift the card to"
          onCloseModal={() =>
            setOpenShiftModal({
              open: false,
              isPartial: false,
            })
          }
          onSubmit={() => {
            if (selectedColumns?.length === 0) {
              toast.error('Please select next columns');
            } else {
              handleShiftColumn({
                isPartial: openShiftModal?.isPartial,
                nextColumns: selectedColumns,
              });
            }
          }}
        >
          {() => {
            return (
              <>
                <div className="flex gap-x-4">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                  />
                  <label>Select All</label>
                </div>
                {getNextColumns()?.map((column, idx) => (
                  <div className="flex gap-x-4" key={idx}>
                    <input
                      type="checkbox"
                      checked={selectedColumns.includes(column.label)}
                      onChange={(e) => handleCheckBoxChange(e, column.label)}
                    />
                    <label>{column.label}</label>
                  </div>
                ))}
              </>
            );
          }}
        </Modal>
      )}
      {isReasonModalOpen && (
        <Modal
          title="Delay Reason"
          description="Enter reason for delay"
          onCloseModal={() => setIsReasonModalOpen(false)}
          onSubmit={async () => {
            let canMarkAsDone = true;
            let currentColumn = column?.page?.label;
            if (
              currentColumn === 'Quotation' &&
              !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                'quotation'
              )
            ) {
              for (let step of card?.steps) {
                if (
                  step?.stepPage === 'Quotation' &&
                  (step?.data?.quoteStatus === 'pending' ||
                    step?.data?.quoteStatus === 'rejected')
                ) {
                  canMarkAsDone = false;
                  break;
                }
              }
            }

            // if (defaultParam?.projectDefaults?.disableApprovals) {
            //   canMarkAsDone = true;
            // }

            if (canMarkAsDone) {
              let nextColumns = getNextColumns();
              nextColumns = nextColumns?.map((elem) => elem?.label);
              if (isLastColumn() === true) {
                dispatch({
                  type: 'SHIFT_CARD',
                  payload: {
                    data: card,
                    nextColumns: ['Completed'],
                    currentColumn: column?.page?.label,
                  },
                });
                await shiftKanbanColumn({
                  id: card?._id,
                  page: column?.page?.label,
                  isComplete: true,
                  data: {
                    delayReason: reasonForDelay,
                  },
                });
                setIsReasonModalOpen(false);
              } else {
                dispatch({
                  type: 'SHIFT_CARD',
                  payload: {
                    data: card,
                    nextColumns: nextColumns ? nextColumns : selectedColumns,
                    currentColumn: column?.page?.label,
                  },
                });
                await shiftKanbanColumn({
                  id: card?._id,
                  page: column?.page?.label,
                  isComplete: false,
                  data: {
                    delayReason: reasonForDelay,
                    nextColumns: nextColumns ? nextColumns : selectedColumns,
                  },
                });
                setIsReasonModalOpen(false);
              }
            }
          }}
        >
          {() => {
            return (
              <>
                <div className="w-full flex justify-between items-center mb-4">
                  <label className="text-md  text-gray-700 font-medium block">
                    Reason for Delay
                  </label>
                  <div className="flex gap-2  items-center">
                    <label className="text-md  text-gray-700 font-medium ">
                      Common to All
                    </label>
                    <input
                      type="checkbox"
                      checked={isCommonToAllChecked}
                      onChange={(e) => {
                        const commonVal = reasonForDelay[0]?.reason;
                        if (e.target.checked && commonVal !== '') {
                          setIsCommonToAllChecked(e.target.checked);
                          setReasonForDelay((prev) =>
                            prev.map((item) => ({ ...item, reason: commonVal }))
                          );
                        } else {
                          toast.info('Add First Field');
                          setReasonForDelay((prev) =>
                            prev.map((item) => ({ ...item, reason: '' }))
                          );
                        }
                      }}
                    />
                  </div>
                </div>
                {reasonForDelay?.map((item, index) => (
                  <div key={index} className="mb-2">
                    <div className="flex gap-2">
                      <p className="text-gray-500 text-sm font-semibold">
                        Delay On
                      </p>{' '}
                      <input
                        type="date"
                        className="text-sm"
                        value={item.delayDate}
                        disabled={true}
                        onChange={(e) => {
                          const newDelayReasons = [...reasonForDelay];
                          newDelayReasons[index].delayDate = e.target.value;
                          setReasonForDelay(newDelayReasons);
                        }}
                      />
                    </div>

                    <Textarea
                      placeholder="Enter reason for delay"
                      className="text-sm"
                      value={item.reason}
                      onChange={(e) => {
                        const newDelayReasons = [...reasonForDelay];
                        newDelayReasons[index].reason = e.target.value;
                        setReasonForDelay(newDelayReasons);
                        setIsCommonToAllChecked(false);
                      }}
                    />
                  </div>
                ))}

                {!isLastColumn() && getNextColumns()?.length > 1 && (
                  <div>
                    <label className="text-sm text-gray-700 font-medium block">
                      Select Next Columns
                    </label>
                    <MultiSelect
                      closeMenuOnSelect={false}
                      className="!w-full"
                      placeholder="Select Next Columns"
                      options={getNextColumns()}
                      value={selectedColumns}
                      onChange={(e) => {
                        setSelectedColumns(e.target.value);
                      }}
                    />
                  </div>
                )}
              </>
            );
          }}
        </Modal>
      )}
      {card?.isAdding ? (
        <div className="bg-gray-200 h-[400px] w-full text-gray-200 rounded-xl animate-pulse">
          <Spinner />
        </div>
      ) : (
        <div
          className={`${userModal ? 'z-50' : ''} shadow-md py-[2px] text-xs bg-white rounded-xl border flex flex-col gap-y-1 !w-full`}
          // ref={dragRef}
        >
          <div
            className={`px-1 justify-between ${tile === true ? 'flex flex-col sm:flex-row md:flex-row items-start md:items-start' : 'flex flex-col items-start'}`}
          >
            {renderCard({
              data: getStep(),
              firstStep: card?.steps?.[0],
              updatedAt: card?.updatedAt,
              column,
              taskId: card?.taskId,
              customTaskId: card?.customTaskId,
              tile,
              page: flowArray
                ? flowArray[
                    flowArray?.findIndex(
                      (item) => item?.label === column?.page?.label
                    )
                  ]
                : '',
              openHistorySidebar,
              tooltipIds,
              setShowSelectTags,
              handleFileChange,
              media: card?.media,
              handleDeleteMedia,
            })}

            <div className={`gap-2 w-fit flex items-center`}>
              {canUpgrade() &&
                column?.page?.label !== 'Completed' &&
                tile !== true && (
                  <button
                    onClick={onUpgrade}
                    className="transition my-2 bg-blue-500 text-white border-2 border-blue-500 !py-[2px] !px-[5px] rounded-xl hover:text-blue-500 hover:bg-white hover:border-blue-500 min-w-fit"
                  >
                    <div className="flex items-center">
                      <p className="mr-1 text-[12px]">Edit</p>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-3 h-3"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"
                        />
                      </svg>
                    </div>
                  </button>
                )}
              {/* {!canUpgrade() &&
                column?.page?.label !== 'Completed' &&
                tile === true && (
                  <button className="transition mx-auto my-2 bg-white text-white border-2 border-white !py-[2px] !px-[5px] rounded-xl min-w-fit cursor-auto">
                    <div className="flex items-center">
                      <p className="mr-1 text-[12px]">Edit </p>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-3 h-3"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"
                        />
                      </svg>
                    </div>
                  </button>
                )} */}
              {canUpgrade() &&
                column?.page?.label !== 'Completed' &&
                tile === true && (
                  <button
                    onClick={onUpgrade}
                    className="transition mx-auto my-2 bg-blue-500 text-white border-2 border-blue-500 !py-[2px] !px-[5px] rounded-xl hover:text-blue-500 hover:bg-white hover:border-blue-500 min-w-fit"
                  >
                    <div className="flex items-center">
                      <p className="mr-1 text-[12px]">Edit</p>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-3 h-3"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"
                        />
                      </svg>
                    </div>
                  </button>
                )}

              {column?.page?.label !== 'Completed' && (
                <button
                  onClick={() => {
                    let canMarkAsDone = true;
                    setInitialScrollIndex(index);
                    let currentColumn = column?.page?.label;
                    if (
                      currentColumn === 'Quotation' &&
                      !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                        'quotation'
                      )
                    ) {
                      for (let step of card?.steps) {
                        if (
                          step?.stepPage === 'Quotation' &&
                          (step?.data?.quoteStatus === 'pending' ||
                            step?.data?.quoteStatus === 'rejected')
                        ) {
                          canMarkAsDone = false;
                          break;
                        }
                      }
                    }
                    // if (defaultParam?.projectDefaults?.disableApprovals) {
                    //   canMarkAsDone = true;
                    // }
                    if (canMarkAsDone) {
                      if (remainingTime.isExceed) {
                        setDatesForDelayReason();
                        setIsReasonModalOpen(true);
                      } else {
                        let nextColumns = getNextColumns();
                        nextColumns = nextColumns?.map((elem) => elem?.label);
                        if (nextColumns?.length > 1) {
                          if (isLastColumn()) {
                            handleShiftColumn({ isPartial: false });
                          } else {
                            setOpenShiftModal({
                              open: true,
                              isPartial: false,
                            });
                          }
                        } else {
                          handleShiftColumn({
                            nextColumns: nextColumns,
                            isPartial: false,
                          });
                        }
                      }
                    } else {
                      toast.error('Quotation approval pending');
                    }
                  }}
                  className="transition  my-2 bg-purple-500 text-white border-2 border-purple-500 !py-[2px] px-[5px] rounded-xl hover:text-purple-500 hover:bg-white hover:border-purple-500 min-w-fit"
                >
                  <div className="flex items-center">
                    <p className="mr-1 text-[12px]">Done</p>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-3 h-3"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m4.5 12.75 6 6 9-13.5"
                      />
                    </svg>
                  </div>
                </button>
              )}
              <div className="mr-3 md:mr-0">
                {column?.page?.label !== 'Completed' && (
                  <button
                    onClick={() => {
                      let canMarkAsDone = true;
                      let currentColumn = column?.page?.label;
                      if (
                        currentColumn === 'Quotation' &&
                        !defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                          'quotation'
                        )
                      ) {
                        for (let step of card?.steps) {
                          if (
                            step?.stepPage === 'Quotation' &&
                            (step?.data?.quoteStatus === 'pending' ||
                              step?.data?.quoteStatus === 'rejected')
                          ) {
                            canMarkAsDone = false;
                            break;
                          }
                        }
                      }
                      // if (defaultParam?.projectDefaults?.disableApprovals) {
                      //   canMarkAsDone = true;
                      // }
                      if (canMarkAsDone) {
                        let nextColumns = getNextColumns();
                        nextColumns = nextColumns?.map((elem) => elem?.label);
                        if (nextColumns?.length > 1) {
                          if (isLastColumn()) {
                            handleShiftColumn({ isPartial: true });
                          } else {
                            setOpenShiftModal({
                              open: true,
                              isPartial: true,
                            });
                          }
                        } else {
                          handleShiftColumn({
                            nextColumns: nextColumns,
                            isPartial: true,
                          });
                        }
                      } else {
                        toast.error('Quotation approval pending');
                      }
                    }}
                    className="transition mx-auto my-2 text-[10px] bg-fuchsia-400 text-white border-2 border-fuchsia-400 !py-[2px] px-[5px] rounded-xl hover:text-fuchsia-400 hover:bg-white hover:border-fuchsia-400 min-w-fit"
                  >
                    <div className="flex items-center">
                      <p className="mr-1 text-[12px]">Forward</p>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-3 h-3"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="m4.5 12.75 6 6 9-13.5"
                        />
                      </svg>
                    </div>
                  </button>
                )}
              </div>
            </div>
            {tile !== true && (
              <div
                className={`items-center mx-3 gap-2 justify-center w-fit flex`}
              >
                {card?.tags?.length > 1 ? (
                  <span className={blueTagClass} title={card?.tags.join(' ,')}>
                    {card?.tags[0]} (+{card?.tags.length - 1})
                  </span>
                ) : card?.tags?.length !== 0 ? (
                  <span className={blueTagClass}>{card?.tags[0]}</span>
                ) : (
                  ''
                )}
              </div>
            )}
          </div>
          <hr className={`${tile === true ? 'hidden' : ''}`} />
          <div
            className={`${tile === true ? 'hidden' : ''} flex items-center justify-between pl-2 `}
          >
            <div className="w-fit ml-[2%]  !text-[9px]">
              {column?.duration && (
                <p
                  className={`${remainingTime.isExceed ? 'text-red-500' : 'text-green-500'} font-semibold`}
                >
                  {remainingTime.days} days {remainingTime.hours} hrs{' '}
                  {remainingTime.minutes} mins {remainingTime.seconds} secs
                </p>
              )}
            </div>
            <div className="w-[25%] flex items-center justify-around">
              <div
                className="hover:cursor-pointer"
                data-tooltip-id="user-tooltip"
                data-tooltip-content="Assign User!"
                data-tooltip-place="top"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-3 h-3"
                  onClick={() => {
                    setUserModal(!userModal);
                    if (reminderModal) setReminderModal(false);
                  }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                  />
                </svg>
                <Tooltip id="user-tooltip" />
              </div>
              <div
                className="hover:cursor-pointer"
                data-tooltip-id="reminder-tooltip"
                data-tooltip-content="Set Reminder!"
                data-tooltip-place="top"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-3 h-3"
                  onClick={() => {
                    setReminderModal(!reminderModal);
                    if (userModal) {
                      setUserModal(false);
                    }
                  }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                  />
                </svg>
                <Tooltip id="reminder-tooltip" />
              </div>
              <div
                className="hover:cursor-pointer !mt-1"
                data-tooltip-id="options-tooltip"
                data-tooltip-content="View Options"
                data-tooltip-place="top"
              >
                {card?.archive.includes(user?._id) ? (
                  <Table.Options
                    onUnarchive={() => handleArchive(card._id)}
                    className={`!py-0 !px-0 mt-1 !-bottom-0`}
                  />
                ) : (
                  <Table.Options
                    onArchive={() => handleArchive(card._id)}
                    className={`!py-0 !px-0 mt-1 !-bottom-0`}
                  />
                )}
                <Tooltip id="options-tooltip" />
              </div>
            </div>
          </div>
          {userModal && (
            <div
              onClick={() => setUserModal(false)}
              className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-50"
            >
              <div
                onClick={(e) => e.stopPropagation()}
                className="flex flex-col justify-around elative w-[420px] bg-white border border-gray-200 shadow-md rounded-lg p-2"
              >
                <section className="w-full flex flex-col">
                  <label
                    className="mb-1"
                    style={{ fontSize: '13px', fontWeight: 'bold' }}
                    htmlFor="projectId"
                  >
                    Assign Users
                  </label>
                  <MultiSelect
                    closeMenuOnSelect={false}
                    className="!w-full"
                    placeholder="Assign Users"
                    options={employees?.map((employee) => ({
                      value: employee?._id,
                      label: employee?.name,
                    }))}
                    value={selectedUsers}
                    onChange={(e) => {
                      setSelectedUsers(e.target.value);
                    }}
                  />
                </section>
                <div className="flex items-center justify-end">
                  <Button
                    className="mt-4 !py-1"
                    isLoading={isLoading}
                    onClick={handleUpdateAssignUser}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          )}
          {showSelectTags && (
            <div
              onClick={() => setShowSelectTags(false)}
              className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-50"
            >
              <div
                onClick={(e) => e.stopPropagation()}
                className="flex flex-col justify-around elative w-[420px] h-64 bg-white border border-gray-200 shadow-md rounded-lg p-2"
              >
                <section className="w-full flex flex-col">
                  <div className="w-full flex justify-between py-2">
                    <label
                      className="mb-1"
                      style={{ fontSize: '13px', fontWeight: 'bold' }}
                      htmlFor="projectId"
                    >
                      Select Tags
                    </label>
                    <label
                      onClick={() => setShowSelectTags(false)}
                      className="cursor-pointer"
                    >
                      ❌
                    </label>
                  </div>
                  <MultiSelect
                    closeMenuOnSelect={false}
                    className="!w-full"
                    placeholder="Select Tags"
                    onChange={(e) => setSelectedTags(e.target.value)}
                    value={selectedTags}
                    options={tags?.map((tag) => ({ value: tag, label: tag }))}
                  />
                </section>
                <div className="mt-4 overflow-y-scroll h-full">
                  {selectedTags?.map((tag, idx) => (
                    <span
                      key={idx}
                      className="flex flex-col py-2 text-md text-gray-400 font-semibold border-y-[1px] mx-4"
                    >
                      {tag?.label}
                    </span>
                  ))}
                </div>
                <div className="flex items-center justify-end">
                  <Button className="mt-4 !py-1" onClick={handleAssignTags}>
                    Save
                  </Button>
                </div>
              </div>
            </div>
          )}
          {reminderModal && (
            <div
              className="fixed inset-0 flex items-center justify-center z-100 bg-gray-900 bg-opacity-50"
              onClick={() => setReminderModal(false)}
            >
              <div
                className="relative w-[420px] bg-white border border-gray-200 shadow-md rounded-lg p-2"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex flex-col items-start w-full">
                  <div className="w-full">
                    <label className="mb-1 text-sm font-bold">
                      Set Reminder
                    </label>
                    <div className="flex items-center gap-1 w-full">
                      <Input
                        type="date"
                        onChange={(e) =>
                          setReminderData((prev) => ({
                            ...prev,
                            date: e.target.value,
                          }))
                        }
                        value={reminderData.date}
                        wrapperClassName="w-full"
                      />
                      <Input
                        type="time"
                        onChange={(e) =>
                          setReminderData((prev) => ({
                            ...prev,
                            time: e.target.value,
                          }))
                        }
                        value={reminderData.time}
                        wrapperClassName="w-full"
                      />
                    </div>
                  </div>

                  <div className="w-full">
                    <label>Remarks</label>
                    <Textarea
                      placeholder="Enter Remarks"
                      value={reminderRemarks}
                      onChange={(e) => setReminderRemarks(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex items-center justify-end">
                  <Button
                    className="mt-4 !py-1"
                    isLoading={isLoading}
                    onClick={handleUpdateRemainderDateAndTime}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
}

export default Card;
