import { useEffect, useRef } from 'react';
import ColumnV3 from './ColumnV3';

function BoardV3({
  isMobile,
  isTablet,
  columns,
  initialScrollIndex = 0,
  visibleTiles,
  setInitialScrollIndex,
  setHistorySidebar,
  FilterHeadingvalue,
  Filtervalue,
  searchTerm,
  searchField,
}) {
  let tiles = visibleTiles?.map((elem) => (elem?.label ? elem?.label : elem));
  const scrollRef = useRef(null);
  useEffect(() => {
    if (
      scrollRef.current &&
      initialScrollIndex >= 0 &&
      initialScrollIndex < columns?.length
    ) {
      let columnWidth = 0;
      for (let i = 0; i < initialScrollIndex; i++) {
        columnWidth += scrollRef?.current?.childNodes[i]?.offsetWidth + 12;
      }

      scrollRef.current.scrollTo({
        left: columnWidth,
        behavior: 'smooth',
      });
    }
  }, [initialScrollIndex, columns]);

  return (
    <>
      <div
        className={`w-full flex gap-x-[7px] !scroll-smooth overflow-scroll`}
        ref={scrollRef}
      >
        {columns?.map((item, idx) => {
          if (tiles?.includes(item?.label)) {
            return (
              <div key={idx} className={`relative`}>
                <ColumnV3
                  isMobile={isMobile}
                  isTablet={isTablet}
                  column={item}
                  index={idx}
                  setInitialScrollIndex={setInitialScrollIndex}
                  setHistorySidebar={setHistorySidebar}
                  FilterHeadingvalue={FilterHeadingvalue}
                  Filtervalue={Filtervalue}
                  searchTerm={searchTerm}
                  searchField={searchField}
                />
              </div>
            );
          }
        })}
      </div>
    </>
  );
}

export default BoardV3;
