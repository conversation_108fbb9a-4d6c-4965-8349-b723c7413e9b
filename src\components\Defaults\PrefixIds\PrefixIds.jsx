import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  useGetPrefixIdQuery,
  useUpdatePrefixIdMutation,
} from '../../../slices/prefixIdApiSlice';
import { PREFIX_ID_LABELS } from '../../../utils/Constant';
import Button from '../../global/components/Button';
import ButtonGroup from '../../global/components/ButtonGroup';
import Spinner from '../../global/components/Spinner';
import PrefixIdFormat from './PrefixIdFormat';

const inventoryIdTypes = [
  'storeId',
  'vendorId',
  'partsId',
  'productId',
  'saId',
  'chalanId',
  'inventoryBatchNo',
  'lotNo',
  'grnId',
  'assetsId',
];

const accountingIdTypes = ['voucherId', 'invoiceId', 'journalId'];

function PrefixIds() {
  const [prefixIds, setPrefixIds] = useState({});
  // only use the edit data state for update function only always empty by default
  // instead of sending the whole prefixids to update body only send the ones that are edited
  const [editData, setEditData] = useState({});
  const [selectTab, setSelectTab] = useState('General');

  const { data: prefixIdsData, isLoading } = useGetPrefixIdQuery();
  const [updatePrefixIds] = useUpdatePrefixIdMutation();

  useEffect(() => {
    if (prefixIdsData) {
      // eslint-disable-next-line no-unused-vars
      const { _id, __v, profileId, lastUsedIndex, ...rest } = prefixIdsData;
      setPrefixIds(JSON.parse(JSON.stringify(rest)));
    }
  }, [prefixIdsData]);

  const validatePrefixIds = () => {
    const errorIds = new Set();
    const prefixes = Object?.entries(editData);
    for (let i = 0; i < prefixes.length; i++) {
      const [idType, idVal] = prefixes[i];
      if (!['qr', 'itemsPerHour', 'changeOverTime']?.includes(idType))
        for (let ii = 0; ii < idVal.length; ii++) {
          const types = Object?.keys(idVal?.[ii])?.join(',');
          const matches = types?.match(/Increment|UserEntry/g);
          if (!matches || matches?.length === 0) {
            errorIds.add(PREFIX_ID_LABELS?.[idType]);
          }
        }
    }
    const ids = [...errorIds];
    return ids;
  };

  const handleUpdate = async () => {
    const check = validatePrefixIds();
    if (check?.length > 0) {
      toast.error(
        `${check?.join(', ')} has missing increment/userentry type for one of its id`
      );

      return;
    }

    const res = await updatePrefixIds({
      data: {
        _id: prefixIdsData?._id,
        ...editData,
      },
    }).unwrap();
    if (res) {
      toast.success('Prefix ids updated');
    }
  };
  const filteredPrefixes = Object?.entries(prefixIds || {})?.filter((item) => {
    if (selectTab === 'General') {
      return (
        !inventoryIdTypes?.includes(item?.[0]) &&
        !accountingIdTypes?.includes(item?.[0])
      );
    } else if (selectTab === 'Inventory') {
      return inventoryIdTypes?.includes(item?.[0]);
    } else if (selectTab === 'Account Management') {
      return accountingIdTypes?.includes(item?.[0]);
    }
  });
  if (isLoading) {
    return <Spinner />;
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between w-full">
        <ButtonGroup
          buttons={['General', 'Inventory', 'Account Management']}
          value={selectTab}
          onChange={setSelectTab}
        />

        <Button onClick={handleUpdate}>Update</Button>
      </div>
      {(filteredPrefixes || [])?.map(([key, val]) => {
        return (
          <PrefixIdFormat
            key={key}
            idType={key}
            format={val}
            setFormat={setPrefixIds}
            setEditData={setEditData}
          />
        );
      })}
    </div>
  );
}

export default PrefixIds;
