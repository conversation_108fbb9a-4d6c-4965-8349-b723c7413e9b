/* eslint-disable no-unused-vars */
import { ArrowDown, ArrowUp } from 'lucide-react';
import { Fragment, useContext, useEffect, useState } from 'react';
import QRCode from 'react-qr-code';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import { ReactComponent as Briefcase } from '../assets/svgs/briefcase.svg';
import Modal from '../components/Modal';
import { InfoTooltip } from '../components/global/components/InfoTooltip';
import Input from '../components/global/components/Input.jsx';
import {
  default as InspectionModal,
  default as ModalGlobal,
} from '../components/global/components/Modal.jsx';
import NewInput from '../components/global/components/NewInput';
import Pagination from '../components/global/components/Pagination';
import Table from '../components/global/components/Table';
import AddEntry from '../components/v3/HomeInventory/AddEntry';
import StockNavigatorInPage from '../components/v3/HomeInventory/StockNavigatorInPage';
import {
  getCorrespondingConversionFactor,
  getLocalDate,
  getLocalDateTime,
  getPartVariantName,
  getProductVariantName,
  mobileWidth,
  tabletWidth,
} from '../helperFunction';
import { useLazyCheckCreateDeviceTypeQuery } from '../slices/createDeviceApiSlice';
import { useLazyGetFormByIdQuery } from '../slices/createFormapiSlice.js';
import {
  useAddInPageMutation,
  useAddManyInPagesMutation,
  useDeleteInPageMutation,
  useDeleteManyInPageMutation,
  useGetInPagePagesQuery,
  useGetInPagesQuery,
  useLazyGetInPagePagesQuery,
  useLazyGetInPagesQuery,
  useLazyLatestGRNQuery,
} from '../slices/inPageApiSlice';
// import { useLazyGetPartsQuery } from '../slices/partApiSlice.js';
import { Button } from 'antd';
import { Plus, Search, Trash, Upload } from 'lucide-react';
import Marquee from 'react-fast-marquee';
import { AiOutlineFileAdd, AiOutlineStock } from 'react-icons/ai';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Downloadicon from '../assets/images/download.png';
import pdf from '../assets/images/pdf.png';
import PendingStockIn from '../components/CreateGRN/PendingStockIn.jsx';
import PreviewInspectionForm from '../components/Forms/PreviewInspectionForm.jsx';
import MediaSidebarData from '../components/MediaSidebarData.jsx';
import TaskTable from '../components/TaskContainer/TaskTable';
import CustomToolTip from '../components/global/CustomToolTip.jsx';
import ExportButton from '../components/global/components/ExportButton.jsx';
import Filter from '../components/global/components/Filter';
import FullScreenModal from '../components/global/components/FullScreenModal.jsx';
import Header from '../components/global/components/Header.jsx';
import RightSidebar from '../components/global/components/RightSidebar.jsx';
import Select from '../components/global/components/Select.jsx';
import Spinner from '../components/global/components/Spinner.jsx';
import TablePopup from '../components/global/components/TablePopup.jsx';
import Tooltip from '../components/global/components/ToolTip.jsx';
import { tableTypes } from '../components/v3/InventoryMasters/inventoryConstants.js';
import MediaModal from '../components/v3/global/components/MediaModal.jsx';
import { downloadMedia } from '../helperFunction.js';
import useDebounceValue from '../hooks/useDebounceValue.js';
import useHeaderAndFooter from '../hooks/useHeaderAndFooter';
import { useGetCustomColumnsQuery } from '../slices/customCoulmn.ApiSlice.js';
import { useGetMediaByIdArrayMutation } from '../slices/mediaSlice.js';
import { useLazyGetPartsByNameQuery } from '../slices/partApiSlice';
import { useLazyGetPurchaseOrderQuery } from '../slices/purchaseOrderApiSlice.js';
import { useLazyGetStoresQuery } from '../slices/storeApiSlice';
import { useLazyGetSubAssemblyByNameQuery } from '../slices/subAssemblySlice';
import { useLazyGetVendorsQuery } from '../slices/vendorApiSlice';
import { Store } from '../store/Store';
import { PAGINATION_LIMIT } from '../utils/Constant';
import WithSelectAll from '../utils/HOC/WithSelectAll.js';
import StockInSideBar from './StockInSideBar.jsx';
import PreviewMedia from './inventory/product-masters/PreviewMedia.jsx';

const importBtn =
  'bg-green-600 text-white rounded-md text-gray-button px-4 text-sm h-[28px] border-r border-gray-200 flex items-center gap-1 hover:bg-green-500 -mr-[3px] transition-all ease-in-out duration-300';

const btnStyling =
  'bg-gray-50 rounded-md text-gray-button !px-4 text-[13px] !p-0 !h-7 border border-gray-200 flex items-center gap-1 hover:bg-gray-100 mb-3 transition-all ease-in-out duration-300';

const _inputStyling =
  'border-2 border-[#E5E8EC] rounded-md focus:outline-none h-10 w-1/2';

const InPage = ({
  handleCheckBoxChange,
  handleSelectAll,
  selectAll,
  checkedRows,
  setCheckedRows,
  rows,
  setRows,
}) => {
  const [getlatestGRN, latestGRN] = useLazyLatestGRNQuery();
  const { data: allInPages } = useGetInPagesQuery();
  const filterheadings = [
    {
      label: 'Date',
      value: 'createdAt',
    },
    {
      label: 'Items',
      value: JSON.stringify({
        label: 'part.name',
        value: 'part._id',
        path: 'part',
      }),
    },
    {
      label: 'Products',
      value: JSON.stringify({
        label: 'product.name',
        value: 'product._id',
        path: 'product',
      }),
    },
    { label: 'Batch No', value: 'batchNo' },
    { label: 'Lot No', value: 'lotNo' },
    { label: 'Manufacture Date', value: 'manufacturingDate' },
    { label: 'In Time', value: 'inTime' },
    {
      label: 'Store Worker',
      value: JSON.stringify({
        label: 'workerId.name',
        value: 'workerId._id',
        path: 'workerId',
      }),
    },
    {
      label: 'Store',
      value: JSON.stringify({
        label: 'store.name',
        value: 'store._id',
        path: 'store',
      }),
    },
    {
      label: 'Vendor',
      value: JSON.stringify({
        label: 'vendor.name',
        value: 'vendor._id',
        path: 'vendor',
      }),
    },
    { label: 'Store Area', value: 'storeArea' },
    { label: 'Quantity', value: 'quantity' },
  ];

  // const { data } = useGetInPagesQuery();

  const navigate = useNavigate();
  const [SearchParams, setSearchParams] = useSearchParams({
    tab: 'stockin',
  });

  const [openmediainfo, setOpenAttachedFiles] = useState(false);

  const [getInPagePages, pageData] = useLazyGetInPagePagesQuery();

  // const [getInPages, allData] = useGetInPagesQuery();
  const { defaults, mqttClient } = useContext(Store);
  // const [rows, setRows] = useState([]);
  const [columns, setColumns] = useState([]);
  const [searchTerm, setsearchTerm] = useState('');
  const [PreviewImageData, setPreviewImageData] = useState('');
  const [ShowFullScreenModal, setShowFullScreenModal] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [sidebardata, setsidebardata] = useState('');
  const [field, setField] = useState('createdAt');
  const [type, setType] = useState('desc');
  const [topics, setTopics] = useState([]);
  const [showNavigatorInPage, setShowNavigatorInPage] = useState(false);
  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [deleteInPage] = useDeleteInPageMutation();
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState('');
  const [totalResults, setTotalResults] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [qrValue, setQrValue] = useState('');
  const [csvData, setCsvData] = useState([]);
  const [csvHeaders, setCsvHeaders] = useState([]);
  const [isDeleteVisible, setIsDeleteVisible] = useState(false);
  const [printType, setPrintType] = useState('QR');
  const [popupData, setPopupData] = useState('');
  const [showAdminPopup, setShowAdminPopup] = useState(false);
  const [deleteId, setDeleteId] = useState('');
  // const userRole = JSON.parse(localStorage.getItem('user'))?.user?.role;
  const [adminDetails, setAdminDetails] = useState({
    email: '',
    password: '',
  });
  const [importedData, setImportedData] = useState([]);
  const [SelectedHeading, setSelectedHeading] = useState('');
  const [SelectedHeadingValue, setSelectedHeadingValue] = useState('');

  const [checkCreateDeviceType] = useLazyCheckCreateDeviceTypeQuery();
  const [showInspectionForm, setShowInspectionForm] = useState(false);
  const [getFormById] = useLazyGetFormByIdQuery();

  const [selectedInspectionForm, setSelectedInspectionForm] = useState('');
  const [formData, setFormData] = useState('');
  const [selectedPart, setSelectedPart] = useState('');
  const [isSideBarOpen, setIsSideBarOpen] = useState(false);
  const [sideBarContent, setSideBarContent] = useState({});
  const [ReadMore, setReadMore] = useState(false);
  const [medias, setMedias] = useState([]);
  // const [getParts] = useLazyGetPartsQuery();
  const [currentRow, setCurrentRow] = useState('');
  const [ShowsideBar, setShowsideBar] = useState(false);
  const [getParts] = useLazyGetPartsByNameQuery();
  const [getProducts] = useLazyGetPartsByNameQuery();
  const [getSubAssemblies] = useLazyGetSubAssemblyByNameQuery();
  const [addEntry] = useAddInPageMutation();
  const [addManyEntry, { isLoading: isManyLoading }] =
    useAddManyInPagesMutation();
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [getStores] = useLazyGetStoresQuery();
  const [getVendors] = useLazyGetVendorsQuery();
  const [getPo, { data: poData }] = useLazyGetPurchaseOrderQuery();
  const [headers, setHeaders] = useState([]);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedHeaders, setSelectedHeaders] = useState([]);
  const [filename, setFileName] = useState('');
  const [importData, setImportData] = useState([]);
  const [deleteManyInPage] = useDeleteManyInPageMutation();
  const [showInvoiceSideBar, setShowInvoiceSideBar] = useState(false);
  const [invoicedata, setInvoiceData] = useState([]);
  const { data: allCustomColumns } = useGetCustomColumnsQuery();
  const [allCustomCols, setAllCustomCols] = useState([]);
  const [additionalFields, setAdditionalFields] = useState([]);
  const [getAllMedia] = useGetMediaByIdArrayMutation();
  const [getAllInpages, { isLoading: isCsvLoading }] = useLazyGetInPagesQuery();
  const [isExporting, setIsExporting] = useState(false);

  const debounceSearch = useDebounceValue(searchTerm || '');
  const [clickedRow, setClickedRow] = useState(null);

  const MOBILE_VIEW_HEADERS = ['DATE', 'ITEMS', 'UOM', 'QUANTITY'];
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const [productdetailMedia, setProductDetailsMedia] = useState([]);
  useEffect(() => {
    getPo({ page: page, limit: limit });
  }, [getPo, limit, page]);

  useEffect(() => {
    (async () => {
      await getlatestGRN().unwrap();
    })();
  }, [getlatestGRN]);

  useEffect(() => {
    const temp = allCustomColumns?.filter(
      (col) => col?.pageName === 'grn/invoice'
    );
    setAllCustomCols(temp);
  }, [allCustomColumns]);

  useEffect(() => {
    if (checkedRows?.length > 0) {
      setIsDeleteVisible(true);
    } else {
      setIsDeleteVisible(false);
    }
  }, [checkedRows]);

  const handleFileChange = async (event) => {
    const file = event.target.files[0];

    if (file) {
      let reader = new FileReader();
      reader.onload = async (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        const headers = jsonData[0];
        const dataRows = jsonData.slice(1);
        setImportData(jsonData.slice(1));
        setFileName(firstSheetName);
        setHeaders(
          headers.map((item) => ({ value: item, label: item.toUpperCase() }))
        );
        setShowImportModal(true);
      };
      reader.readAsArrayBuffer(file);
    }
  };

  const downloadImage = (media) => {
    (async () => {
      let response = await fetch(media?.data);
      let data = await response.blob();
      let metadata = {
        type: media?.type,
      };
      let file = new File([data], media?.name, metadata);

      const url = window.URL.createObjectURL(file);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;

      a.download = media?.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    })();
  };

  const handleSubmit = async () => {
    // const idIndex = headers.indexOf('id');
    if (selectedHeaders[12] === '') {
      toast.error('Please select the type column');
      return;
    }
    const type = headers.findIndex(
      (item) => item.value === selectedHeaders[12]
    );
    const items = headers.findIndex(
      (item) => item.value === selectedHeaders[0]
    );
    const batchNo = headers.findIndex(
      (item) => item.value === selectedHeaders[1]
    );
    const lotNo = headers.findIndex(
      (item) => item.value === selectedHeaders[2]
    );
    const manufacturingDate = headers.findIndex(
      (item) => item.value === selectedHeaders[3]
    );
    const inTime = headers.findIndex(
      (item) => item.value === selectedHeaders[4]
    );
    const storeWorker = headers.findIndex(
      (item) => item.value === selectedHeaders[5]
    );
    const store = headers.findIndex(
      (item) => item.value === selectedHeaders[6]
    );
    const vendor = headers.findIndex(
      (item) => item.value === selectedHeaders[7]
    );
    const storeArea = headers.findIndex(
      (item) => item.value === selectedHeaders[8]
    );
    const uom = headers.findIndex((item) => item.value === selectedHeaders[9]);
    const valuation = headers.findIndex(
      (item) => item.value === selectedHeaders[10]
    );
    const quantity = headers.findIndex(
      (item) => item.value === selectedHeaders[11]
    );
    const result = await Promise.all(
      importData.map(async (row) => {
        // let value = null;
        // switch (row[type]) {
        //   case 'Part': {
        //     // only find items if the row[items] exists
        //     if (row[items]) {
        //       value = await getParts({ id: row[items] }).unwrap();
        //     }
        //     break;
        //   }
        //   case 'Product': {
        //     if (row[items]) {
        //       value = await getProducts({ id: row[items] }).unwrap();
        //     }
        //     break;
        //   }
        //   case 'SubAssembly': {
        //     if (row[items]) {
        //       value = getSubAssemblies({ id: row[items] }).unwrap();
        //     }
        //     break;
        //   }
        //   default: {
        //     toast.error('Please make sure type column is correct');
        //   }
        // }
        // if (value === null) {
        //   toast.error(
        //     'Please ensure Items are created and their type is correct'
        //   );
        //   return;
        // }
        // const stores = row[store] ? await getStores(row[store]).unwrap() : null;
        // const vendors = row[vendor]
        //   ? await getVendors(row[vendor]).unwrap()
        //   : null;
        // const vendorActual = vendors?.vendors?.items.filter(
        //   (item) => item.name === row[vendor]
        // );

        const rowData = {
          isReturn: false,
          inventory: {
            type: row[type],
            // id: value?._id,
            quantity: row[quantity] || 0,
            name: row[items] || '',
          },
          batchNo: row[batchNo],
          lotNo: row[lotNo] || 1,
          lotNoFormat: {},
          manufacturingDate: new Date(row[manufacturingDate]) || Date.now(),
          workerId: row[storeWorker] || null,
          // vendor: vendorActual ? vendorActual[0]?._id : null,
          // store: stores?.stores?.items[0]?._id || null,
          vendor: row[vendor] || null,
          store: row[store] || null,
          storeArea: row[storeArea] || '',
          inTime: new Date(row[inTime]) || Date.now(),
          inName: '',
          totalWeight: 0,
          unitWeight: 0,
          wieghtForClac: 0,
          uomForCalc: 0,
          inspectionData: {},
          valuation: row[valuation],
        };

        // addEntry({ ...rowData });
        return rowData;
      })
    );
    const res = addManyEntry({ data: result });
  };

  const getUOMConversionValue = (e) => {
    return (
      e?.selectedUOM?.conversion && (
        <span
          style={{
            whiteSpace: 'nowrap',
          }}
          className=" mt-2 !text-gray-500"
        >
          {e?.selectedUOM?.conversion.conversionValue +
            '-' +
            e?.selectedUOM?.conversion.conversionUnit}
        </span>
      )
    );
  };

  useEffect(() => {
    setCsvData([]);
    getInPagePages({
      page,
      limit,
      field_name: SelectedHeading,
      field_value: SelectedHeadingValue?.length > 2 ? SelectedHeadingValue : '',
      debounceSearch,
      type,
      field,
    })
      .unwrap()
      .catch((err) => {
        toast.error(err?.data?.message, {
          theme: 'colored',
          position: 'top-right',
          toastId: err?.data?.message,
        });
        return;
      });
  }, [
    getInPagePages,
    limit,
    page,
    getInPagePages.apply,
    SelectedHeading,
    SelectedHeadingValue,
    debounceSearch,
    type,
    field,
  ]);

  useEffect(() => {
    let rows = pageData?.data?.results;
    let columns = defaults?.defaultParam?.inPageColumns;
    let headers = [
      {
        label: 'Type',
        key: 'type',
      },
    ];
    for (let i in columns) {
      headers = [
        ...headers,
        {
          label: columns[i].title,
          key: columns[i].field,
        },
      ];
    }
    setCsvHeaders(headers);
    let temp = [];
    for (let i in rows) {
      let row = [];
      let e = rows[i];
      for (let j in columns) {
        let field = columns[j].field;
        if (field === 'items') {
          if (e?.part) {
            row = {
              ...row,
              items: e?.part?.name,
              type: 'Part',
            };
          } else if (e?.partVariant) {
            row = {
              ...row,
              items: getPartVariantName(e?.partVariant),
              type: 'PartVariant',
            };
          } else if (e?.product) {
            row = {
              ...row,
              items: e?.product?.name,
              type: 'Product',
            };
          } else if (e?.productVariant) {
            row = {
              ...row,
              items: getProductVariantName(e?.productVariant),
              type: 'ProductVariant',
            };
          } else {
            row = {
              ...row,
              items: e?.subAssembly?.name,
              type: 'Subassembly',
            };
          }
        } else if (field === 'batchNo') {
          row = {
            ...row,
            batchNo: e?.batchNo,
          };
        } else if (field === 'quantity') {
          row = {
            ...row,
            quantity: e?.quantity,
          };
        } else if (field === 'vendor') {
          if (e?.vendor) {
            row = {
              ...row,
              vendor: e?.vendor?.name,
            };
          }
        } else if (field === 'store') {
          if (e?.store) {
            row = {
              ...row,
              store: e?.store?.name,
            };
          }
        } else if (field === 'storeArea') {
          row = {
            ...row,
            storeArea: e?.storeArea,
          };
        } else if (field === 'inTime') {
          row = {
            ...row,
            inTime: getLocalDateTime(e?.inTime),
          };
        } else if (field === 'manufacturingDate') {
          row = {
            ...row,
            manufacturingDate: getLocalDateTime(e?.manufacturingDate),
          };
        } else if (field === 'worker') {
          row = {
            ...row,
            worker: e?.workerId?.map((worker) => worker?.name).join(','),
          };
        } else if (field === 'uom') {
          row = {
            ...row,
            uom:
              e?.part?.uom ||
              e?.partVariant?.part?.uom ||
              e?.product?.uom ||
              e?.productVariant?.product?.uom ||
              e?.subAssembly?.uom,
          };
        } else if (field === 'valuation') {
          row = {
            ...row,
            valuation:
              e?.part?.valuation ||
              e?.partVariant?.part?.valuation ||
              e?.productVariant?.product?.valuation,
          };
        } else if (field === 'lotNo') {
          row = {
            ...row,
            lotNo: e.lotNo,
          };
        } else {
          row = {
            ...row,
          };
        }
      }
      temp.push(row);
    }
    setCsvData(temp);
  }, [defaults, pageData]);

  useEffect(() => {
    setTotalPages(pageData?.data?.totalPages);
    setTotalResults(pageData?.data?.totalResults);
    setColumns(defaults?.defaultParam?.inPageColumns);
    if (pageData?.data?.results) {
      setRows(pageData?.data?.results);
      setIsPageLoading(false);
    }
  }, [defaults, pageData, setRows]);

  useEffect(() => {
    const fetchDeviceIds = async () => {
      const res2 = await checkCreateDeviceType({ type: 'weight' }).unwrap();

      setTopics(
        res2?.createDevice?.fields
          ?.filter((field) => field.type === 'value')
          .map((field) => field.topic)
      );
    };
    fetchDeviceIds();
  }, [checkCreateDeviceType]);

  useEffect(() => {
    if (showPopup === false && mqttClient?.unsubscribe && topics?.length > 0) {
      mqttClient?.unsubscribe(topics);
    }
  }, [showPopup, topics, mqttClient]);

  const pageVisited = (page - 1) * limit + 1;

  const openQRModal = (data) => {
    const {
      batchNo,
      lotNo,
      part,
      product,
      partVariant,
      productVariant,
      subAssembly,
      quantity,
      createdAt,
    } = data;
    let stringOfQr = `Batch No: ${batchNo}\nLot No: ${lotNo}\nReceived Qty: ${quantity}\nDate: ${getLocalDate(createdAt)}`;

    if (part) {
      stringOfQr += `\nPart: ${part?.name} \nUom: ${part?.uom || '-'}`;
    } else if (product) {
      stringOfQr += `\nProduct: ${product?.name} \nUom: ${product?.uom || '-'}`;
    } else if (productVariant) {
      stringOfQr += `\nProduct: ${productVariant?.product?.name} \nUom: ${productVariant?.product?.uom || '-'}`;
    } else if (partVariant) {
      stringOfQr += `\nPart Variant: ${partVariant?.part?.name} \nUom: ${partVariant?.part?.uom || '-'} `;
    } else if (subAssembly) {
      stringOfQr += `\nSub-Assembly: ${subAssembly?.name} \nUom: ${subAssembly?.uom || '-'}`;
    }

    setQrValue(stringOfQr);
    setShowModal(true);
  };

  const submitAdminForm = async (e) => {
    e.preventDefault();
    if (checkedRows.length > 1) {
      const idsToDelete = checkedRows.map((item) => item._id);
      const res = await deleteManyInPage({
        ids: idsToDelete,
        userDetails: adminDetails,
      });
      setShowAdminPopup(false);
      setCheckedRows([]);
      if (res?.data) {
        toast.success('Batches deleted successfully');
      }
    } else {
      deleteInPage({
        id: deleteId,
        userDetails: adminDetails,
      })
        .unwrap()
        .then(() => {
          setShowAdminPopup(false);
          toast.success('Batch deleted successfully', {
            theme: 'colored',
            position: 'top-right',
          });
        });
    }

    setAdminDetails({
      email: '',
      password: '',
    });
  };

  const fetchAndExport = async () => {
    // Prevent multiple simultaneous exports
    if (isExporting || isCsvLoading) {
      return;
    }

    try {
      setIsExporting(true);
      const allData = await getAllInpages().unwrap();

      // Check if we have data to export
      if (!allData?.inPages || allData.inPages.length === 0) {
        toast.warning('No data available to export.', {
          theme: 'colored',
          position: 'top-right',
        });
        return;
      }

      const headers = [
        [
          'SR. No.',
          'Date',
          'Name',
          'Item Type',
          'Batch No.',
          'UOM',
          'Quantity',
          'Scrap',
          'Store',
        ],
      ];
      const dataToExport = allData?.inPages?.map((data, index) => {
        const itemName = data?.part?.name || data?.product?.name;
        const uom =
          data?.selectedUom?.UOM || data?.part?.uom || data?.product?.uom || '-';
        const scrap = data?.scrapQuantity;
        let quantity;
        if (data?.scrapQuantity) {
          quantity = '-';
        } else if (
          data?.selectedUom?.conversion &&
          data?.selectedUom?.conversion?.conversionValue
        ) {
          quantity =
            parseFloat(data?.quantity) /
            parseFloat(data?.selectedUom?.conversion?.conversionValue);
        } else {
          quantity = data?.quantity;
        }

        return {
          srNo: index + 1,
          date: getLocalDate(data?.createdAt),
          name: `"${itemName}"`,
          type: data?.part ? 'Items' : 'Product',
          batch: data.batchNo || '-',
          uom,
          quantity,
          scrap,
          store: data?.store?.name || '-',
        };
      });

      const ws = XLSX.utils.json_to_sheet(dataToExport, {
        header: [
          'srNo',
          'date',
          'name',
          'type',
          'batch',
          'uom',
          'quantity',
          'scrap',
        ],
      });
      XLSX.utils.sheet_add_aoa(ws, headers, { origin: 'A1' });
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, `Stock In`);
      XLSX.writeFile(wb, `stockIn.csv`, { bookType: 'csv' });

      toast.success('Export completed successfully!', {
        theme: 'colored',
        position: 'top-right',
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Export failed. Please try again.', {
        theme: 'colored',
        position: 'top-right',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const renderInputBasedOnType = (refData, form) => {
    let infoObject = refData[form];
    if (infoObject?.fieldType === 'Date') {
      let options = [
        {
          value: 'gt',
          label: 'Greater Than',
        },
        {
          value: 'lt',
          label: 'Lesser Than',
        },
        {
          value: 'eq',
          label: 'Equal To',
        },
      ];

      return (
        <div className="flex gap-6 items-center w-full">
          <div className="w-full min-w-[80px]">
            {options.filter(
              (option) => option.value === infoObject?.checkCondition
            )[0]?.label ?? '-'}
          </div>

          <div className="w-full min-w-[80px]">{infoObject?.date ?? '-'}</div>
        </div>
      );
    }

    if (infoObject?.fieldType === 'MultiSelect') {
      const val = infoObject?.options?.map((el) => el.value).join(' ,');
      return val;
    }

    if (infoObject?.fieldType === 'Range') {
      return (
        <div className="flex gap-6 items-center w-full">
          <div className="w-full min-w-[80px]">{`Minimum : ${
            infoObject?.min ?? '-'
          }`}</div>

          <div className="w-full min-w-[80px]">{`Maximum : ${
            infoObject?.max ?? '-'
          }`}</div>
        </div>
      );
    }

    if (infoObject?.fieldType === 'DropDown') {
      // const val = infoObject?.options?.map((el) => el.value).join(' ,');
      return infoObject?.value ?? '-';
    }

    if (infoObject?.fieldType == 'Range Threshold') {
      return (
        <div className="flex gap-6 items-center w-full">
          <div className="w-full min-w-[80px]">{`Average : ${
            infoObject?.average ?? '-'
          }`}</div>

          <div className="w-full min-w-[80px]">{`Margin : ${
            infoObject?.margin ?? '-'
          }`}</div>
        </div>
      );
    }

    if (infoObject?.fieldType === 'String') {
      return (
        <div className="flex gap-6 items-center w-full">
          <div className="w-full min-w-[80px]">{infoObject?.string ?? '-'}</div>

          <div className="w-full min-w-[80px]">-</div>
        </div>
      );
    }

    if (infoObject?.fieldType === 'MultiCheckbox') {
      return (
        <div className="flex gap-6 items-center w-full">
          <div className="w-full min-w-[80px]">
            {infoObject?.labelArray?.map((label, index) => {
              return <div key={index}>{label}</div>;
            })}
          </div>

          <div className="w-full min-w-[80px]">-</div>
        </div>
      );
    }

    if (
      infoObject?.fieldType === 'Check' ||
      infoObject?.fieldType === 'Media' ||
      infoObject?.filedType === 'Min-Max'
    ) {
      return (
        <div className="flex gap-6 items-center w-full">
          <div className="w-full min-w-[80px]">-</div>

          <div className="w-full min-w-[80px]">-</div>
        </div>
      );
    }
  };

  const { header, footer } = useHeaderAndFooter({});

  useEffect(
    function () {
      async function getForm() {
        const form = await getFormById({
          id: selectedInspectionForm,
        }).unwrap();
        setSelectedInspectionForm(form?._id);
        setFormData(form);
      }
      if (selectedInspectionForm) {
        getForm();
      }
    },
    [getFormById, selectedInspectionForm, showInspectionForm]
  );

  // useEffect(
  //   function () {
  //     async function fetchParts() {
  //       const parts = await getParts().unwrap();
  //       console.log(parts);
  //     }
  //     fetchParts();
  //   },
  //   [getParts]
  // );
  useEffect(() => {
    if (showImportModal) {
      const newSelectedHeaders = columns?.map((item, idx) => {
        if (headers[idx]?.value === item?.title) {
          return item?.title;
        } else {
          return '';
        }
      });
      // Set 'type' as a selected header
      const typeIndex = columns?.findIndex((item) => item.field === 'type');

      if (typeIndex === -1) {
        newSelectedHeaders[12] = 'type';
      }

      // Update the state with the newSelectedHeaders array
      setSelectedHeaders(newSelectedHeaders);
    }
  }, [columns, headers, showImportModal]);

  const handleMediaFiles = (Mediadata) => {
    setsidebardata(Mediadata);
    setShowsideBar((prev) => !prev);
  };

  useEffect(() => {
    const uniqueHeadings = new Set();
    invoicedata?.[invoicedata.length - 1]?.additionalFields.forEach((field) => {
      uniqueHeadings.add(field);
    });
    setAdditionalFields(Array.from(uniqueHeadings));
  }, [invoicedata]);

  useEffect(() => {
    const getMedia = async () => {
      const allmediainfo = await getAllMedia({
        data: { ids: [...(sideBarContent?.part?.media || [])] },
      });

      setProductDetailsMedia(allmediainfo?.data?.media);
    };

    if (openmediainfo) {
      getMedia();
    }
  }, [openmediainfo, sideBarContent]); //eslint-disable-line

  return (
    <div>
      {isMobile && clickedRow && (
        <TablePopup
          onBack={() => setClickedRow(null)}
          isEdit={false}
          isDownload={false}
        >
          <div className="space-y-4 !text-[12px]">
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">ITEM</label>
              <p>{clickedRow?.part?.name}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">BATCH NO.</label>
              <p>{clickedRow?.batchNo ?? '-'}</p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">LOT NO.</label>
              <p>{clickedRow?.lotNo ?? '0'}</p>
            </div>

            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">MANUFACTURE DATE</label>
              <p>{getLocalDateTime(clickedRow?.manufacturingDate)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">IN TIME</label>
              <p>{getLocalDateTime(clickedRow?.inTime)}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">STORE WORKER</label>
              <p>{clickedRow?.workerId?.name}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">STORE</label>
              <p>{clickedRow?.store?.name ?? '-'}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">VENDOR</label>
              <p>{clickedRow?.vendor?.name ?? '-'}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">STORE AREA</label>
              <p>{clickedRow?.storeArea ?? '-'}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">UOM</label>
              <p>{clickedRow?.part?.uom ?? '-'}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">VALUATION METHOD</label>
              <p>
                {clickedRow?.part?.valuation ||
                  clickedRow?.product?.valuation ||
                  clickedRow?.subAssembly?.valuation}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold ">QUANTITY</label>
              <p>{clickedRow?.quantity ?? '-'}</p>
            </div>
          </div>
        </TablePopup>
      )}

      {openmediainfo && (
        <PreviewMedia
          data={productdetailMedia}
          handleClose={() => {
            setProductDetailsMedia([]);
            setOpenAttachedFiles(false);
          }}
        />
      )}

      <FullScreenModal
        ShowModal={ShowFullScreenModal}
        setShowModal={setShowFullScreenModal}
      >
        <>
          <embed
            src={PreviewImageData}
            type="application/pdf"
            width="100%"
            height="100%"
          />
        </>
      </FullScreenModal>
      {showInvoiceSideBar && (
        <RightSidebar
          openSideBar={showInvoiceSideBar}
          setOpenSideBar={setShowInvoiceSideBar}
        >
          <>
            <h3>Invoice Data</h3>
            <div className="table-section overflow-x-scroll">
              <h4 className="mt-8 mb-2">All Invoices</h4>
              <Table>
                <Table.Row>
                  <Table.Th>Invoice Id</Table.Th>
                  <Table.Th>Comment</Table.Th>
                  <Table.Th>Invoice Amount</Table.Th>
                  <Table.Th>Files</Table.Th>
                  {additionalFields?.map((field, idx) => (
                    <Table.Th key={idx}>{field.label}</Table.Th>
                  ))}
                </Table.Row>

                <Table.Body>
                  {invoicedata?.map((invoice, idx) => {
                    return (
                      <Table.Row key={idx}>
                        <Table.Td>{invoice?.invoiceId}</Table.Td>
                        <Table.Td>{invoice?.invoiceComment}</Table.Td>
                        <Table.Td>{invoice?.invoiceAmount}</Table.Td>
                        <Table.Td className={`!text-blue-500 !font-semibold`}>
                          file-{invoice?.files?.length || '-'}
                        </Table.Td>
                        {/* We are mapping on ExtraHeading to make the Table Alignment Sync */}
                        {additionalFields?.map((_, idx) => (
                          <Table.Td key={idx}>
                            {invoice.additionalFields[idx]?.data || '-'}
                          </Table.Td>
                        ))}
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
          </>
        </RightSidebar>
      )}
      {ShowsideBar && (
        <RightSidebar
          scale={736}
          openSideBar={ShowsideBar}
          setOpenSideBar={setShowsideBar}
        >
          <MediaSidebarData
            allInPages={allInPages}
            setPreviewImageData={setPreviewImageData}
            setShowFullScreenModal={setShowFullScreenModal}
            setShowsideBar={setShowsideBar}
            downloadImage={downloadImage}
            Downloadicon={Downloadicon}
          />
        </RightSidebar>
      )}
      {ReadMore && (
        <MediaModal
          FormData={medias}
          isView={true}
          setShowModal={setReadMore}
          ShowModal={ReadMore}
        />
      )}
      {showModal && (
        <Modal>
          <div className="flex flex-col justify-center items-center gap-5 w-full h-full">
            <h1 className="">Batch No: {popupData.batchNo}</h1>
            <h3 className="">Lot No: {popupData.lotNo}</h3>
            {popupData?.part ? (
              <p className="text-lg font-normal mb-10">
                Part: {popupData?.part?.name}
              </p>
            ) : popupData?.product ? (
              <p className="text-lg font-normal mb-10">
                Product: {popupData?.product?.name}
              </p>
            ) : (
              <p className="text-lg font-normal mb-10">
                SubAssembly: {popupData?.subAssembly?.name}
              </p>
            )}
            <QRCode
              id={printType === 'QR' ? 'print-qr' : ''}
              size={200}
              value={qrValue}
            />
            <div className="flex flex-row gap-12">
              <button
                className={btnStyling}
                onClick={() => setShowModal(false)}
              >
                Close
              </button>
              <button
                className={btnStyling}
                onClick={() => {
                  setPrintType('QR');
                  setTimeout(() => {
                    window.print();
                  }, 500);
                }}
              >
                Print
              </button>
            </div>
          </div>
        </Modal>
      )}
      {isSideBarOpen && (
        <RightSidebar
          scale={736}
          openSideBar={isSideBarOpen}
          setOpenSideBar={setIsSideBarOpen}
        >
          <StockInSideBar
            data={sideBarContent}
            setIsSideBarOpen={setIsSideBarOpen}
            setOpenAttachedFiles={setOpenAttachedFiles}
            setShowInspectionForm={setShowInspectionForm}
            setReadMore={setReadMore}
            setMedias={setMedias}
          />
        </RightSidebar>
      )}
      {showAdminPopup && (
        <Modal>
          <form
            className="flex flex-col justify-center items-center gap-5 w-full h-full"
            onSubmit={submitAdminForm}
          >
            <h1>Admin Login</h1>
            <NewInput
              type="email"
              placeholder="Enter email"
              onChange={(e) =>
                setAdminDetails((prev) => ({
                  ...prev,
                  email: e?.target?.value,
                }))
              }
            />
            <NewInput
              type="password"
              placeholder="Enter Password"
              onChange={(e) =>
                setAdminDetails((prev) => ({
                  ...prev,
                  password: e?.target?.value,
                }))
              }
            />
            <div className="flex items-center gap-2">
              <button className={btnStyling}>Submit</button>
              <button
                className={btnStyling}
                type="button"
                onClick={() => {
                  setShowAdminPopup(false);
                }}
              >
                Close
              </button>
            </div>
          </form>
        </Modal>
      )}
      {showPopup && (
        // <Modal setModalOpen={setShowPopup}>
        <AddEntry
          setShowPopup={setShowPopup}
          topics={topics}
          setTopics={setTopics}
        />
        // </Modal>
      )}
      {showImportModal && (
        <ModalGlobal
          description={`Easily connect fields for a smooth and effortless data import process`}
          title={'IMPORT CSV'}
          onCloseModal={() => {
            setShowImportModal(false);
            setHeaders([]);
            setFileName('');
            setImportData([]);
          }}
          onSubmit={() => {
            setShowImportModal(false);
            handleSubmit();
          }}
        >
          {() => {
            return (
              <>
                <div>
                  The best match to each field on the selected file have been
                  auto selected
                </div>
                <div className="flex gap-20">
                  <div>
                    <p className="font-bold text-xl mb-3">Existing Headers</p>
                    {columns?.map((col, idx) => (
                      <div
                        className="flex h-[45px] items-center mb-3"
                        key={idx}
                      >
                        <div>
                          <p>{col?.title}</p>
                        </div>
                      </div>
                    ))}
                    <div key={12} className="flex h-[45px] items-center mb-3">
                      <div>
                        <p>Type</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <p className="font-bold text-xl mb-3">
                      Imported File Headers
                    </p>
                    {headers?.map((item, idx) => (
                      <div
                        key={idx}
                        className={`${idx > columns?.length ? 'hidden' : ''}`}
                      >
                        <Select
                          options={headers}
                          key={idx}
                          className={'mb-3'}
                          value={selectedHeaders[idx]}
                          onChange={(e) =>
                            setSelectedHeaders((prev) => {
                              const updatedHeaders = [...prev];
                              updatedHeaders[idx] = e.target.value;
                              return updatedHeaders;
                            })
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </>
            );
          }}
        </ModalGlobal>
      )}
      <div>
        <div className="flex justify-between my-3">
          <Header
            title="Stock In"
            description=""
            infoTitle="Welcome to the Stock In Page"
            infoDesc="Your streamlined solution for managing
              stock-in entries."
            paras={[
              'This page allows you to effortlessly perform "Stock In" actions for parts, raw materials, or products.',
              'With a simple item search, you can complete stock-in entries with ease. Moreover, each entry generates a unique QR code,enhancing user convenience and tracking.',
              'For comprehensive stock management, the In Page also provides a detailed stock-in history, ensuring you have all the information you need at your fingertips. Simplify your stock-in process and enhance traceability with our intuitive In Page.',
              'Simplify stock-in entries with unique QR code generation and access to comprehensive stock-in history',
            ]}
          />

          {SearchParams?.get('tab') === 'stockin' && (
            <div className="flex gap-2">
              <CustomToolTip
                tooltipId="filter-tooltip"
                content="Filter"
                place="top"
                effect="solid"
                className="bg-black text-white p-1 rounded"
              >
                <Filter
                  className="!right-0"
                  headingfilteroptions={filterheadings}
                  secondfilterdata={pageData?.data?.results}
                  setSelectedHeading={setSelectedHeading}
                  setSelectedValue={setSelectedHeadingValue}
                />
              </CustomToolTip>
              <Button
                type="primary"
                icon={<Plus />}
                onClick={() => {
                  navigate('/inventory/inpage/grn');
                }}
              >
                Create GRN
              </Button>
            </div>
          )}
        </div>

        <div className="flex flex-col md:flex-row items-start md:items-center justify-between md:gap-0">
          <div className="flex items-end gap-3 mb-4 md:mb-0">
            <div className="task-manager-btn">
              <Button
                type={
                  SearchParams?.get('tab') === 'task' ? 'primary' : 'default'
                }
                onClick={() => {
                  setSearchParams(
                    (prev) => {
                      prev.set('tab', 'task');
                      return prev;
                    },
                    { replace: true }
                  );
                }}
              >
                Task Manager
                <InfoTooltip
                  position="bottom"
                  id="taskmanager"
                  isHtml={true}
                  content="When a Purchase Order (PO) is approved, the task manager
                  automatically updates to reflect this status and includes the
                  expected delivery date."
                />
              </Button>
            </div>
            {defaults?.defaultParam?.projectDefaults?.pendingStockIn && (
              <div>
                <Button
                  type={
                    SearchParams?.get('tab') === 'pendingStockIn'
                      ? 'primary'
                      : 'default'
                  }
                  onClick={() => {
                    setSearchParams(
                      (prev) => {
                        prev.set('tab', 'pendingStockIn');
                        return prev;
                      },
                      { replace: true }
                    );
                  }}
                >
                  Pending Stock In
                  <InfoTooltip
                    position="bottom"
                    id="pendingStockIn"
                    isHtml={true}
                    content="This Tab Shows the Pending Inpages which not stocked in yet"
                  />
                </Button>
              </div>
            )}
            <Button
              type={
                SearchParams?.get('tab') === 'stockin' ? 'primary' : 'default'
              }
              icon={<AiOutlineStock />}
              onClick={() => {
                setSearchParams(
                  (prev) => {
                    prev.set('tab', 'stockin');
                    return prev;
                  },
                  { replace: true }
                );
              }}
            >
              Stocks
            </Button>
          </div>
          {SearchParams?.get('tab') === 'stockin' && (
            <div className="flex items-center">
              <div className="flex gap-2 items-center justify-end w-full mt-5 mr-2 md:mt-0">
                {!isMobile && (
                  <div className="relative group mt-[-4px]">
                    <CustomToolTip
                      tooltipId="export-tooltip"
                      content="Export"
                      place="top"
                      effect="solid"
                      className="bg-black text-white p-1 rounded"
                    >
                      <ExportButton
                        csvData={csvData}
                        csvHeaders={csvHeaders}
                        setPrintType={setPrintType}
                        className="!mt-[2px] !px-0 !py-0 !border-none !text-[13px] !h-[28px]"
                        isCsvLoading={isCsvLoading || isExporting}
                        fetchAndExport={fetchAndExport}
                      />
                    </CustomToolTip>
                  </div>
                )}

                {!isMobile && (
                  <Button
                    className="!text-[10px] lg:!text-[12px] !py-2"
                    onClick={() => setShowNavigatorInPage((prev) => !prev)}
                  >
                    Stock Navigator
                    <InfoTooltip position="right" id="stockNavigator">
                      Stock Navigator is your tool for efficient item location
                      searches within the warehouse.
                    </InfoTooltip>
                  </Button>
                )}

                {!isMobile && (
                  <Button
                    type="primary"
                    icon={<Upload className="w-4 h-4" />}
                    onClick={() =>
                      document.getElementById('file-input').click()
                    }
                    className="!min-w-[50px] !text-[10px] lg:!text-[12px]"
                  >
                    Import
                    <input
                      id="file-input"
                      type="file"
                      onClick={(e) => {
                        e.target.value = null;
                      }}
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </Button>
                )}
              </div>
              <div className="relative w-full">
                <Search className="absolute left-4 top-5 transform -translate-y-1/2 w-5 h-5 text-gray-400 transition-colors duration-200 group-hover:text-blue-500" />
                <input
                  className="w-[200px] lg:!w-[370px] pl-12 pr-4 py-[8px] text-sm rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 hover:border-gray-300"
                  placeholder="Search All..."
                  onChange={(e) => setsearchTerm(e.target.value)}
                />
              </div>
            </div>
          )}
        </div>

        {showNavigatorInPage && (
          <StockNavigatorInPage
            showNavigatorInPage={showNavigatorInPage}
            setShowNavigatorInPage={setShowNavigatorInPage}
          />
        )}
        {SearchParams?.get('tab') === 'stockin' ? (
          <>
            {showInspectionForm && (
              <InspectionModal
                title={'Inspection Form'}
                description={''}
                svg={<Briefcase className="h-8 w-8" />}
                isSubmitRequired={false}
                onCloseModal={() => {
                  setShowInspectionForm(false);
                  setSelectedInspectionForm('');
                  setSelectedPart('');
                  setCurrentRow('');
                  setFormData('');
                }}
              >
                {() => {
                  return (
                    <>
                      <PreviewInspectionForm
                        inspectionData={sideBarContent?.inspectionData}
                        inspectionRefData={
                          sideBarContent?.inspectionRefData ||
                          (sideBarContent?.part
                            ? sideBarContent?.part?.defaultFormData
                            : sideBarContent?.product?.sideBarContent?.product
                                ?.defaultFormData) ||
                          {}
                        }
                      />
                    </>
                  );
                }}
              </InspectionModal>
            )}

            <section
              className="w-full overflow-x-scroll"
              id={printType === 'exportTable' ? 'print-only' : ''}
            >
              {header()}
              {isPageLoading ? (
                <Spinner />
              ) : (
                <>
                  <Table>
                    <Table.Head>
                      <Table.Row>
                        {/* <th className='font-medium w-15 text-sm'></th> */}
                        {!isMobile && (
                          <Table.Th>
                            {checkedRows.length > 0 ? (
                              <div>
                                <input
                                  type="checkbox"
                                  className="mr-2"
                                  checked={selectAll}
                                  onChange={(e) => handleSelectAll(e)}
                                />
                                Select All
                              </div>
                            ) : (
                              ''
                            )}
                          </Table.Th>
                        )}
                        <Table.Th>
                          <div className="flex">
                            <div>Date</div>
                            {type === 'aesc' && field === 'createdAt' ? (
                              <ArrowUp
                                cursor={'pointer'}
                                size={15}
                                onClick={() => {
                                  setField('createdAt');
                                  setType('desc');
                                }}
                              />
                            ) : (
                              <ArrowDown
                                cursor={'pointer'}
                                size={15}
                                onClick={() => {
                                  setField('createdAt');
                                  setType('aesc');
                                }}
                              />
                            )}
                          </div>
                        </Table.Th>
                        {columns
                          ?.filter(
                            (el) =>
                              el?.title === 'Items' ||
                              el?.title === 'Batch No.' ||
                              el?.title === 'UOM' ||
                              el?.title === 'Quantity'
                          )
                          ?.map((e) => {
                            const isHide =
                              isMobile &&
                              !MOBILE_VIEW_HEADERS.includes(
                                e.title.toUpperCase()
                              );
                            return (
                              !isHide && (
                                <Table.Th key={e?.title} className={`!w-9`}>
                                  {e?.title === 'Items' ? (
                                    <div className="flex">
                                      <div>{e?.title}</div>
                                      {/* {type === 'aesc' &&
                                      field === 'part.name' ? (
                                        <ArrowUp
                                          cursor={'pointer'}
                                          size={15}
                                          onClick={() => {
                                            setField('part.name');
                                            setType('desc');
                                          }}
                                        />
                                      ) : (
                                        <ArrowDown
                                          cursor={'pointer'}
                                          size={15}
                                          onClick={() => {
                                            setField('part.name');
                                            setType('aesc');
                                          }}
                                        />
                                      )} */}
                                    </div>
                                  ) : (
                                    e?.title
                                  )}
                                </Table.Th>
                              )
                            );
                          })}
                        <Table.Th>Scrap</Table.Th>
                        <Table.Th>Qr Code</Table.Th>
                        {/* <Table.Th>Comment</Table.Th>
                        
                        <Table.Th
                        // id={printType === 'exportTable' ? 'print-qr' : ''}
                        >
                          Actions
                        </Table.Th>
                        <Table.Th>Files</Table.Th>
                        <Table.Th>Inspection Form</Table.Th>
                        <Table.Th>Invoice Details</Table.Th> */}
                      </Table.Row>
                    </Table.Head>
                    <Table.Body>
                      {rows?.map((e) => {
                        const conversion = getCorrespondingConversionFactor(
                          e?.part ? e?.part?.uom : e?.partVariant?.part?.uom,
                          e?.part ? e?.part : e?.partVariant?.part
                        );
                        return (
                          <Table.Row key={e._id}>
                            {/* <td>
                      <input type="checkbox" onClick={(ev) => addToDeleteList(e._id)} />
                      </td> */}
                            {!isMobile && (
                              <Table.Td>
                                <input
                                  type="checkbox"
                                  onChange={(event) => {
                                    handleCheckBoxChange(event, e);
                                    setDeleteId(e?._id);
                                  }}
                                  checked={checkedRows.includes(e)}
                                />
                              </Table.Td>
                            )}
                            <Table.Td>{getLocalDate(e?.createdAt)}</Table.Td>

                            {columns
                              ?.filter(
                                (el) =>
                                  el?.title === 'Items' ||
                                  el?.title === 'Batch No.' ||
                                  el?.title === 'UOM' ||
                                  el?.title === 'Quantity'
                              )
                              ?.map((col) => {
                                const isHide =
                                  isMobile &&
                                  !MOBILE_VIEW_HEADERS.includes(
                                    col?.title?.toUpperCase()
                                  );
                                return (
                                  !isHide && (
                                    <Table.Td
                                      key={col.field}
                                      className={
                                        '!max-w-[24rem] !w-[24rem] cursor-pointer'
                                      }
                                      onClick={() => {
                                        if (isMobile) {
                                          setClickedRow(e);
                                        } else {
                                          setSideBarContent(e);
                                          setIsSideBarOpen(true);
                                        }
                                      }}
                                    >
                                      {(() => {
                                        switch (col.field) {
                                          case 'items':
                                            return e?.part ? (
                                              <div>
                                                <p>
                                                  {e?.part?.name?.length > 0 ? (
                                                    e?.part?.name?.length <=
                                                    55 ? (
                                                      e?.part?.name
                                                    ) : (
                                                      <Tooltip
                                                        text={e?.part?.name}
                                                      >
                                                        {e?.part?.name?.substring(
                                                          0,
                                                          55
                                                        ) + '...'}
                                                      </Tooltip>
                                                    )
                                                  ) : (
                                                    <Table.Td>{'-'}</Table.Td>
                                                  )}
                                                </p>
                                              </div>
                                            ) : e?.product ? (
                                              <div>
                                                <p>
                                                  {e?.product?.name?.length >
                                                  0 ? (
                                                    e?.product?.name?.length <=
                                                    55 ? (
                                                      e?.product?.name
                                                    ) : (
                                                      <Tooltip
                                                        text={e?.product?.name}
                                                      >
                                                        {e?.product?.name?.substring(
                                                          0,
                                                          55
                                                        ) + '...'}
                                                      </Tooltip>
                                                    )
                                                  ) : (
                                                    <Table.Td>{'-'}</Table.Td>
                                                  )}
                                                </p>
                                              </div>
                                            ) : e?.partVariant ? (
                                              <div>
                                                <p>
                                                  {e?.partVariant?.name
                                                    ?.length > 0 ? (
                                                    getPartVariantName(
                                                      e?.partVariant
                                                    )?.length <= 55 ? (
                                                      getPartVariantName(
                                                        e?.partVariant
                                                      )
                                                    ) : (
                                                      <Tooltip
                                                        text={getPartVariantName(
                                                          e?.partVariant
                                                        )}
                                                      >
                                                        {getPartVariantName(
                                                          e?.partVariant
                                                        )?.substring(0, 55) +
                                                          '...'}
                                                      </Tooltip>
                                                    )
                                                  ) : (
                                                    <Table.Td>{'-'}</Table.Td>
                                                  )}
                                                </p>
                                              </div>
                                            ) : e?.productVariant ? (
                                              <div>
                                                <p>
                                                  {`${e?.productVariant?.product?.name}-${e?.productVariant?.name}(${e?.productVariant?.variantId})`
                                                    ?.length > 0 ? (
                                                    `${e?.productVariant?.product?.name}-${e?.productVariant?.name}(${e?.productVariant?.variantId})`
                                                      ?.length <= 85 ? (
                                                      `${e?.productVariant?.product?.name}-${e?.productVariant?.name}(${e?.productVariant?.variantId})`
                                                    ) : (
                                                      <Tooltip
                                                        text={`${e?.productVariant?.product?.name}-${e?.productVariant?.name}(${e?.productVariant?.variantId})`}
                                                      >
                                                        {`${e?.productVariant?.product?.name}-${e?.productVariant?.name}(${e?.productVariant?.variantId})`?.substring(
                                                          0,
                                                          85
                                                        ) + '...'}
                                                      </Tooltip>
                                                    )
                                                  ) : (
                                                    <Table.Td>{'-'}</Table.Td>
                                                  )}
                                                </p>
                                              </div>
                                            ) : e?.product ? (
                                              <div>
                                                <p>
                                                  {e?.product?.name?.length ? (
                                                    e?.product?.name?.length <=
                                                    55 ? (
                                                      e?.product?.name
                                                    ) : (
                                                      <Tooltip
                                                        text={e?.product?.name}
                                                      >
                                                        {e?.product?.name?.substring(
                                                          0,
                                                          55
                                                        ) + '...'}
                                                      </Tooltip>
                                                    )
                                                  ) : (
                                                    <Tooltip
                                                      text={e?.product?.name}
                                                    >
                                                      {e?.product?.name?.substring(
                                                        0,
                                                        55
                                                      ) + '...'}
                                                    </Tooltip>
                                                  )}
                                                </p>
                                              </div>
                                            ) : (
                                              <Table.Td>{'-'}</Table.Td>
                                            );

                                          case 'batchNo':
                                            return (
                                              <div className="flex gap-3">
                                                <p className="my-auto">
                                                  {e.batchNo ? (
                                                    <Tooltip
                                                      text={`batch-no(${e.batchNo}) - lot no(${e.lotNo || 0})`}
                                                    >
                                                      {e?.batchNo
                                                        ? `${e.batchNo} (${e.lotNo || 0})`
                                                        : '-'}
                                                    </Tooltip>
                                                  ) : (
                                                    '-'
                                                  )}
                                                </p>
                                              </div>
                                            );

                                          case 'quantity':
                                            return (
                                              <p>
                                                {e?.scrapQuantity
                                                  ? '-'
                                                  : e?.selectedUOM &&
                                                      e?.selectedUOM?.conversion
                                                    ? parseFloat(e?.quantity) /
                                                      parseFloat(
                                                        e?.selectedUOM
                                                          ?.conversion
                                                          ?.conversionValue
                                                      )
                                                    : e?.quantity}
                                              </p>
                                            );

                                          case 'uom':
                                            return e?.part ? (
                                              <>
                                                <p>
                                                  {e?.selectedUOM
                                                    ? e?.selectedUOM?.uom
                                                    : e?.part?.uom}
                                                </p>
                                                {getUOMConversionValue(e)}
                                              </>
                                            ) : e?.product ? (
                                              <p>
                                                <p>
                                                  {(e?.selectedUOM
                                                    ? e?.selectedUOM?.uom
                                                    : e?.product?.uom) || '-'}
                                                </p>
                                                {getUOMConversionValue(e)}
                                              </p>
                                            ) : e?.partVariant ? (
                                              <>
                                                <p>
                                                  {(e?.selectedUOM
                                                    ? e?.selectedUOM?.uom
                                                    : e?.partVariant?.part
                                                        ?.uom) || '-'}
                                                </p>
                                                {getUOMConversionValue(e)}
                                              </>
                                            ) : (
                                              <p>NOS</p>
                                            );

                                          // default:
                                          //   // Handle default rendering, ensuring it's a valid type
                                          //   const value = e[col.field];
                                          //   return (
                                          //     <p>
                                          //       {typeof value === 'object'
                                          //         ? JSON.stringify(value)
                                          //         : value || ''}
                                          //     </p>
                                          //   );
                                        }
                                      })()}
                                    </Table.Td>
                                  )
                                );
                              })}

                            <Table.Td>{e?.scrapQuantity || '-'}</Table.Td>
                            <Table.Td>
                              <button
                                onClick={() => {
                                  openQRModal(e);
                                  setPopupData(e);
                                }}
                              >
                                <QRCode value={e?._id} size={20} />
                              </button>
                            </Table.Td>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                </>
              )}

              {footer()}
            </section>
            {!isManyLoading && (
              <Pagination
                limit={limit}
                page={page}
                totalPages={totalPages}
                totalResults={totalResults}
                setPage={setPage}
                setLimit={setLimit}
              />
            )}
          </>
        ) : defaults?.defaultParam?.projectDefaults?.pendingStockIn &&
          SearchParams?.get('tab') === 'pendingStockIn' ? (
          <>
            <PendingStockIn />
          </>
        ) : (
          <TaskTable />
        )}
      </div>
    </div>
  );
};

export default WithSelectAll(InPage);
