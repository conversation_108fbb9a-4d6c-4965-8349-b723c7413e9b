import {
  DownloadOutlined,
  ExportOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
} from '@ant-design/icons';
import { Button, Dropdown } from 'antd';
import { CSVLink } from 'react-csv';
import { printWithSpecialFileName } from '../../../helperFunction';

const ExportButton = ({
  csvData,
  csvHeaders,
  csvClick,
  setPrintType,
  hasMaster,
  setShowSelectableExport,
  customFunc = () => {},
  customPrintFunc,
  fetchAndExport,
  isCsvLoading,
  hidePdf = false,
}) => {
  const handleExportPDF = () => {
    if (customPrintFunc) {
      customPrintFunc();
      return;
    }

    customFunc();
    if (setPrintType) {
      setPrintType('exportTable');
      setTimeout(() => {
        printWithSpecialFileName();
        setPrintType('QR');
      }, 500);
    } else {
      setTimeout(printWithSpecialFileName, 500);
    }
  };

  const menuItems = [
    ...((csvData && csvHeaders) || csvClick
      ? [
          {
            key: 'csv',
            label: csvClick ? (
              <div onClick={csvClick} className="flex items-center gap-2">
                <FileExcelOutlined />
                Export as CSV
              </div>
            ) : (
              <CSVLink
                filename="transactions.csv"
                data={csvData}
                headers={csvHeaders}
                className="flex items-center gap-2 text-inherit"
              >
                <FileExcelOutlined />
                Export as CSV
              </CSVLink>
            ),
          },
        ]
      : []),
    ...(!hidePdf
      ? [
          {
            key: 'pdf',
            label: (
              <div
                onClick={handleExportPDF}
                className="flex items-center gap-2"
              >
                <FilePdfOutlined />
                Export as PDF
              </div>
            ),
          },
        ]
      : []),
    ...(hasMaster
      ? [
          {
            key: 'master',
            label: (
              <div
                onClick={() => setShowSelectableExport((prev) => !prev)}
                className="flex items-center gap-2"
              >
                <DownloadOutlined />
                Export Master
              </div>
            ),
          },
        ]
      : []),
    ...(fetchAndExport
      ? [
          {
            key: 'exportAll',
            label: (
              <div
                onClick={(e) => {
                  if (isCsvLoading) {
                    e.preventDefault();
                    e.stopPropagation();
                    return;
                  }
                  fetchAndExport();
                }}
                className={`flex items-center gap-2 ${isCsvLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                  }`}
              >
                <ExportOutlined />
                Export All
              </div>
            ),
          },
        ]
      : []),
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      trigger={['click']}
      placement="bottomRight"
    >
      <Button
        type="primary"
        loading={isCsvLoading}
        icon={<DownloadOutlined />}
        className="flex items-center gap-2"
      >
        Export
      </Button>
    </Dropdown>
  );
};

export default ExportButton;
