import { LuTags } from 'react-icons/lu';
import { Tooltip } from 'react-tooltip';

import CircularUploadButton from '../CircularUploadButton';

export const renderCard = ({
  // data,
  // firstStep,
  updatedAt,
  column,
  taskId,
  selectedTags,
  customTaskId,
  tile = false,
  // page = 'End Flow',
  openHistorySidebar,
  setShowSelectTags,
  handleFileChange,
  handleDeleteMedia,
  // media,
  card,
}) => {
  let showEditedTag = false;
  // let editId = '';
  for (let i of card?.steps) {
    if (i?.stepNumber === card?.currentStep && i?.stepPage === column?.label) {
      showEditedTag = true;
      // editId = i?._id;
      break;
    }
  }

  const getProductName = () => {
    if (typeof card?.productObject?.details === 'string') {
      return card?.productObject?.details;
    } else if (typeof Object.values(card?.productObject)?.[0] === 'string') {
      return Object.values(card?.productObject)?.[0];
    } else if (
      typeof Object.values(card?.productObject)?.[0]?.value === 'string'
    ) {
      return Object.values(card?.productObject)?.[0]?.value;
    } else {
      return 'N/A';
    }
  };
  // function capitalizeFirstLetter(str) {
  //   if (!str) return;
  //   return str.charAt(0).toUpperCase() + str.slice(1);
  // }

  // function truncateId(id, len) {
  //   return id && id.length > len ? `${id.slice(0, len)}...` : id;
  // }

  const lastStep =
    card?.steps?.findLast(
      (i) => i.stepPage === card?.currentPage[card?.currentPage?.length - 1]
    ) || {};

  return (
    <>
      <div className={'ml-2 text-xs w-full'}>
        <div className="flex justify-between items-center">
          <div className="flex gap-[10px] items-center flex-wrap">
            <span
              className={`!max-w-[202rem] !text-[9px] bg-blue-100 cursor-pointer hover:bg-blue-500 hover:text-white transition text-blue-500 px-3 py-[2px] mt-2 rounded-lg font-medium whitespace-nowrap`}
              onClick={openHistorySidebar}
              data-tooltip-id={`node-${taskId}tooltip-${customTaskId}`}
            >
              <div
                className="hover:cursor-pointer  !text-sm"
                data-tooltip-id={`node-${taskId}-tooltip`}
                data-tooltip-content={
                  customTaskId && customTaskId !== 'null'
                    ? customTaskId.length > 10
                      ? `${customTaskId.slice(0, 100)}...`
                      : `${customTaskId}`
                    : taskId
                }
                data-tooltip-place="top"
              >
                <span style={{ cursor: 'pointer', fontSize: '10px' }}>
                  {customTaskId && customTaskId !== 'null'
                    ? customTaskId.length > 10
                      ? `${customTaskId.slice(0, 100)}...`
                      : `${customTaskId}`
                    : taskId}
                </span>
                <Tooltip
                  id={`node-${taskId}-tooltip`}
                  style={{ zIndex: '99' }}
                  place="top"
                  className="   break-words text-wrap max-w-[200px]"
                />
              </div>
            </span>
            {showEditedTag && (
              <span className=" text-[12px] mt-2 py-1 px-2 rounded-[10px] text-center bg-blue-600 text-white font-semibold">
                Edited
              </span>
            )}
            {card?.productObject &&
              (getProductName()?.length <= 10 ? (
                <span className="text-[12px] mt-2 py-1 px-2 rounded-[10px] text-center bg-green-400 text-white font-semibold">
                  {getProductName()}
                </span>
              ) : (
                <span
                  className="cursor-pointer text-[12px] mt-2 py-1 px-2 rounded-[10px] text-center bg-green-400 text-white font-semibold"
                  data-tooltip-id={`product-details-tooltip-${taskId}`}
                  data-tooltip-content={getProductName()}
                >
                  {getProductName()?.slice(0, 10) + '..'}
                  <Tooltip
                    id={`product-details-tooltip-${taskId}`}
                    place="top"
                    className="break-words max-w-[200px]"
                  />
                </span>
              ))}

            {lastStep?.status ? (
              <span className="text-[12px] mt-2 py-1 px-2 rounded-[10px] text-center bg-green-500 text-white font-semibold capitalize">
                {lastStep?.status}
              </span>
            ) : (
              <>
                {/* show currentStatus ==="Converted" if available  for leads column*/}
                {card?.currentStatus && column?.label === 'Leads' && (
                  <span className="text-[12px] mt-2 py-1 px-2 rounded-[10px] text-center bg-green-500 text-white font-semibold">
                    {card?.currentStatus}
                  </span>
                )}
                {/* show currentStatus ==="approved" if available  for quotation column" */}
                {card?.currentStatus === 'Approved' &&
                  column?.label === 'Quotation' && (
                    <span className="text-[12px] mt-2 py-1 px-2 rounded-[10px] text-center bg-green-500 text-white font-semibold">
                      {card?.currentStatus}
                    </span>
                  )}
                {/* show currentStatus ==="approved" if available  for sales order column" */}
                {card?.currentStatus === 'approved' &&
                  column?.label === 'Sales Order' && (
                    <span className="text-[12px] mt-2 py-1 px-2 rounded-[10px] text-center bg-green-500 text-white font-semibold">
                      {card?.currentStatus}
                    </span>
                  )}
              </>
            )}

            {selectedTags?.length > 0 && (
              <div
                style={{
                  backgroundColor: `${selectedTags?.[0]?.value ? selectedTags?.[0]?.value?.color : selectedTags?.[0]?.color}`,
                }}
                className="px-2 py-1 text-[0.6rem] rounded-[10px] text-white mt-2"
              >
                {selectedTags?.[0]?.label
                  ? selectedTags?.[0]?.label
                  : selectedTags?.[0]?.name}
              </div>
            )}
            {selectedTags?.length > 1 && (
              <p className="bg-slate-200 px-[6px] py-[6px] rounded-[10px] text-[10px] mt-2 font-semibold text-slate-400">
                + {selectedTags?.length - 1}
              </p>
            )}
            {/* {selectedTags?.map((tag, idx) => {
              <span className="text-[12px] mt-2 py-1 px-4 rounded-[10px] text-center bg-yellow-200 text-white font-semibold">
                Edited
              </span>
            )}
            {selectedTags?.map((tag, idx) => {
              return (
                <div
                  key={idx}
                  style={{
                    backgroundColor: `${tag?.value ? tag?.value?.color : tag?.color}`,
                  }}
                  className="px-2 py-1 text-[0.6rem] rounded-[10px] text-white mt-2"
                >
                  {tag?.label ? tag?.label : tag?.name}
                </div>
              );
            })} */}
            <div className="border-purple-400 border-dotted border-2 rounded-[50%] p-1 mt-[8px] hover:bg-purple-100  cursor-pointer">
              <LuTags
                size={13}
                title="Edit Tags"
                onClick={() => setShowSelectTags(true)}
                className="text-purple-400"
              />
            </div>
            {/* <span
            className={`bg-orange-100 text-orange-500 px-3 py-1 rounded-lg font-medium whitespace-nowrap ${tile === true ? 'hidden' : ''}`}
            data-tooltip-id={tooltipIds?.stepIdTooltip}
          >
            <Tooltip
              id={tooltipIds?.stepIdTooltip}
              style={{
                backgroundColor: '#1e293b',
                color: 'white',
                fontWeight: 'normal',
                padding: '12px',
                fontSize: '12px',
                maxWidth: '200px',
                zIndex: '1',
                borderRadius: '6px',
                lineHeight: '20px',
              }}
              arrowColor="transparent"
            >
              <p>
                {firstStep?.data?.salesOrderID ||
                  firstStep?.data?.salesInquiryId ||
                  firstStep?.data?.workOrderId ||
                  firstStep?.data?.quoteID}
              </p>
            </Tooltip>
            {truncateId(firstStep?.data?.salesOrderID, 10) ||
              truncateId(firstStep?.data?.salesInquiryId, 10) ||
              truncateId(firstStep?.data?.workOrderId, 10) ||
              truncateId(firstStep?.data?.quoteID, 10)}
          </span> */}
            {/* <span
            className={`px-3 py-1 gap-5 items-center flex rounded-xl font-medium whitespace-nowrap ${tile === true ? '' : 'hidden'}`}
          >
            {page === 'End Flow' ? page : 'Create a new ' + column?.page?.label}
          </span> */}
            {/* {column?.page?.label !== 'Completed' && (
            <span
              className={`${
                data?.stepPage === column?.page?.label
                  ? 'bg-green-100 text-green-500'
                  : 'bg-yellow-100 text-yellow-500'
              }  px-3 py-1 rounded-lg font-medium whitespace-nowrap ${tile === true ? 'hidden' : ''}`}
            >
              {data?.stepPage === column?.page?.label ? 'Created' : 'Pending'}
            </span>
          )} */}
            {/* {data?.stepPage === 'Quotation' && (
            <div className="flex flex-wrap">
              <span
                className={`${
                  data?.data?.quoteStatus === 'Approved'
                    ? 'bg-green-100 text-green-500'
                    : data?.data?.quoteStatus === 'pending'
                      ? 'bg-yellow-100 text-yellow-500'
                      : 'bg-red-100 text-red-500'
                }  px-3 py-1 rounded-lg font-medium whitespace-nowrap ${tile === true ? 'hidden' : ''}`}
              >
                {`Quote ${capitalizeFirstLetter(data?.data?.quoteStatus)}`}
              </span>
            </div>
          )} */}
          </div>
          <span className="mr-4 mt-2">
            <CircularUploadButton
              size={12}
              backgroundColor="bg-green-500"
              hoverColor="bg-green-600"
              onFileChange={handleFileChange}
              handleDeleteMedia={handleDeleteMedia}
              media={card?.media}
            />
          </span>
        </div>
        <div className="flex  gap-2 mt-2">
          {/* <div className="flex items-center gap-2 truncate">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className={`w-2 h-2  ${tile ? 'w-3 h-3' : 'w-2 h-2'}`}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
              />
            </svg>

            <p
              className={`text-gray-400 ${tile ? '!text-[12px]' : '!text-[9px]'} font-medium`}
            >
              {data?.userId?.name || 'name'}
            </p>
          </div> */}
          <div className="flex items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-2 h-2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
              />
            </svg>

            <p
              className={`text-gray-400 ${tile ? '!text-[12px]' : '!text-[9px]'} font-medium`}
            >
              {new Date(updatedAt).toDateString()}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};
